# RAG知识库问答系统部署方案 (AMD GPU + CentOS 7)

本文档提供了在CentOS 7服务器上部署RAG知识库问答系统的详细步骤，特别针对使用AMD GPU的环境进行了优化。

## 1. 环境准备

### 1.1 系统要求

- CentOS 7
- Python 3.10.12
- AMD GPU
- 至少8GB内存，推荐16GB以上
- 至少100GB磁盘空间（用于模型和向量数据库）

### 1.2 安装基础依赖

```bash
# 更新系统
sudo yum update -y

# 安装基本工具
sudo yum install -y wget git vim htop screen tmux

# 安装开发工具
sudo yum groupinstall -y "Development Tools"
sudo yum install -y epel-release
sudo yum install -y cmake3

# 安装文档处理相关依赖
sudo yum install -y libreoffice libreoffice-headless

# 安装OCR依赖
sudo yum install -y tesseract tesseract-langpack-chi-sim
```

### 1.3 安装中文字体（确保中文文档处理）

```bash
# 创建字体目录
sudo mkdir -p /usr/share/fonts/chinese

# 下载并安装字体
cd /usr/share/fonts/chinese
sudo wget https://github.com/adobe-fonts/source-han-sans/raw/release/OTF/SimplifiedChinese/SourceHanSansSC-Regular.otf
sudo wget https://github.com/adobe-fonts/source-han-serif/raw/release/OTF/SimplifiedChinese/SourceHanSerifSC-Regular.otf

# 更新字体缓存
sudo fc-cache -fv
```

## 2. 安装ROCm（AMD GPU支持）

ROCm是AMD GPU的计算平台，相当于NVIDIA的CUDA。

```bash
# 添加ROCm仓库
sudo tee /etc/yum.repos.d/rocm.repo <<'EOF'
[ROCm]
name=ROCm
baseurl=https://repo.radeon.com/rocm/yum/rpm
enabled=1
gpgcheck=0
EOF

# 安装ROCm
sudo yum install -y rocm-dev

# 设置环境变量
echo 'export PATH=$PATH:/opt/rocm/bin' | sudo tee -a /etc/profile.d/rocm.sh
echo 'export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/rocm/lib' | sudo tee -a /etc/profile.d/rocm.sh
source /etc/profile.d/rocm.sh

# 验证安装
rocminfo
```

## 3. Python环境配置

### 3.1 创建虚拟环境

```bash
# 确保pip可用
sudo yum install -y python3-pip

# 安装虚拟环境工具
pip3 install --user virtualenv

# 创建项目目录
mkdir -p /opt/rag_system
cd /opt/rag_system

# 创建虚拟环境
python3 -m virtualenv venv
source venv/bin/activate
```

### 3.2 安装PyTorch（AMD版本）

```bash
# 安装AMD版PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/rocm5.6
```

### 3.3 安装项目依赖

```bash
pip install fastapi uvicorn langchain chromadb
pip install sentence-transformers
pip install pymupdf python-docx python-pptx
pip install openpyxl beautifulsoup4 lxml
pip install python-multipart python-jose[cryptography] passlib[bcrypt]
pip install qdrant-client huggingface-hub
```

## 4. 部署Ollama

Ollama用于运行本地大语言模型。

```bash
# 创建安装目录
mkdir -p ~/ollama_install
cd ~/ollama_install

# 下载Ollama安装脚本
curl -L https://ollama.com/download/ollama-linux-amd64 -o ollama

# 添加执行权限
chmod +x ollama

# 移动到系统路径
sudo mv ollama /usr/local/bin/

# 创建系统服务
sudo tee /etc/systemd/system/ollama.service <<EOF
[Unit]
Description=Ollama Service
After=network.target

[Service]
ExecStart=/usr/local/bin/ollama serve
Restart=always
RestartSec=10
User=root
Environment="PATH=/usr/local/bin:/usr/bin:/bin"
Environment="HOME=/root"

[Install]
WantedBy=multi-user.target
EOF

# 启动Ollama服务
sudo systemctl daemon-reload
sudo systemctl enable ollama
sudo systemctl start ollama

# 拉取模型 (可能需要较长时间，建议使用screen或tmux运行)
ollama pull deepseek-r1:32b
# 如果32b模型太大，可以考虑先使用较小的模型:
# ollama pull deepseek-coder:6.7b
```

## 5. 安装并配置向量数据库

### 5.1 Chroma（默认）

```bash
# Chroma已在前面的依赖中安装好
```

### 5.2 Qdrant（可选）

```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 拉取并运行Qdrant容器
sudo docker pull qdrant/qdrant
sudo docker run -d -p 6333:6333 -p 6334:6334 -v /opt/qdrant_data:/qdrant/storage qdrant/qdrant
```

## 6. 部署应用

### 6.1 克隆项目代码

```bash
# 在/opt/rag_system目录下
cd /opt/rag_system

# 如果是从本地上传，可以使用scp命令:
# scp -r /local/path/to/RAG_demo6.1_sensitiveword_nohistroy/* user@server:/opt/rag_system/

# 或者从Git仓库克隆
# git clone https://your-repo-url.git .
```

### 6.2 创建必要的目录

```bash
mkdir -p log
mkdir -p Private_GPT/source_documents
mkdir -p Private_GPT/db_DOC_basic_600
```

### 6.3 配置修改

创建或修改`config.ini`文件：

```bash
vim config.ini
```

添加以下内容（根据实际情况调整）：

```ini
[PATHS]
LIBREOFFICE_PATH = /usr/bin/soffice
TESSERACT_PATH = /usr/bin

[DATABASE]
PERSIST_DIRECTORY = /opt/rag_system/Private_GPT/db_DOC_basic_600
VECTOR_DB_TYPE = chroma

[DOCUMENTS]
SOURCE_DIRECTORY = /opt/rag_system/Private_GPT/source_documents

[EMBEDDINGS]
MODEL_NAME = BAAI/bge-large-zh-v1.5
EMBEDDING_TYPE = huggingface
BASE_URL = http://localhost:11434
OLLAMA_MODEL = deepseek-r1:32b

[CHUNKING]
CHUNK_SIZE = 600
CHUNK_OVERLAP = 50

[LLM]
LLM_TYPE = ollama
MODEL = deepseek-r1:32b
TARGET_SOURCE_CHUNKS = 4
OLLAMA_BASE_URL = http://localhost:11434

[OCR]
LANGUAGES = chi_sim+eng

[QDRANT]
HOST = localhost
PORT = 6333
COLLECTION_NAME_STORE = documents_store
COLLECTION_NAME_QUERY = documents_query

[API]
BASE_URL = http://your-api-server/api
DEFAULT_PATH = /v1/default
REPOSITORY_PATH = /v1/repository
PLAN_PATH = /v1/plan
CONTENT_TYPE = application/json
TOKEN = your_api_token
```

### 6.4 配置系统服务

创建服务文件：

```bash
sudo tee /etc/systemd/system/rag-system.service <<EOF
[Unit]
Description=RAG Knowledge Base QA System
After=network.target ollama.service

[Service]
User=root
WorkingDirectory=/opt/rag_system
ExecStart=/opt/rag_system/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8089
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable rag-system
sudo systemctl start rag-system
```

### 6.5 配置防火墙

```bash
# 开放系统服务端口
sudo firewall-cmd --permanent --add-port=8089/tcp

# 如果使用Qdrant
sudo firewall-cmd --permanent --add-port=6333/tcp
sudo firewall-cmd --permanent --add-port=6334/tcp

# 应用防火墙规则
sudo firewall-cmd --reload
```

## 7. 配置Nginx（可选但推荐）

### 7.1 安装Nginx

```bash
sudo yum install -y nginx
```

### 7.2 配置Nginx作为反向代理

```bash
sudo tee /etc/nginx/conf.d/rag-system.conf <<EOF
server {
    listen 80;
    server_name your_domain_or_ip;

    location / {
        proxy_pass http://localhost:8089;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }
}
EOF
```

### 7.3 启动Nginx

```bash
sudo systemctl enable nginx
sudo systemctl start nginx
```

## 8. 系统优化

### 8.1 内存优化

```bash
# 调整交换空间大小
sudo dd if=/dev/zero of=/swapfile bs=1G count=16
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# 调整内存分配策略
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 8.2 GPU优化

```bash
# 调整ROCm相关设置
echo 'export HSA_OVERRIDE_GFX_VERSION=10.3.0' | sudo tee -a /etc/profile.d/rocm.sh
source /etc/profile.d/rocm.sh
```

## 9. 监控和维护

### 9.1 日志监控

```bash
# 应用日志
tail -f /opt/rag_system/log/langchain_agent.log

# 系统服务日志
journalctl -u rag-system -f
journalctl -u ollama -f
```

### 9.2 定期备份

创建备份脚本：

```bash
cat > /opt/rag_system/backup.sh <<EOF
#!/bin/bash
BACKUP_DIR="/opt/rag_system/backups"
DATE=\$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p \$BACKUP_DIR

# 备份向量数据库
tar -czf \$BACKUP_DIR/vector_db_backup_\$DATE.tar.gz /opt/rag_system/Private_GPT/db_DOC_basic_600

# 备份源文档
tar -czf \$BACKUP_DIR/documents_backup_\$DATE.tar.gz /opt/rag_system/Private_GPT/source_documents

# 备份配置文件
cp /opt/rag_system/config.ini \$BACKUP_DIR/config_backup_\$DATE.ini

# 保留最近30天的备份
find \$BACKUP_DIR -type f -mtime +30 -delete
EOF

chmod +x /opt/rag_system/backup.sh
```

设置定时任务：

```bash
(crontab -l ; echo "0 2 * * * /opt/rag_system/backup.sh") | crontab -
```

### 9.3 性能监控

安装监控工具：

```bash
sudo yum install -y htop glances
```

设置性能监控：

```bash
# 实时监控系统资源
htop

# 监控GPU状态
/opt/rocm/bin/rocm-smi
```

## 10. 故障排除指南

### 10.1 文档处理问题

如果遇到文档处理失败：

```bash
# 检查LibreOffice安装
sudo yum reinstall -y libreoffice libreoffice-headless

# 检查Tesseract OCR
sudo yum reinstall -y tesseract tesseract-langpack-chi-sim
```

### 10.2 GPU相关问题

如果遇到ROCm/GPU相关问题：

```bash
# 检查ROCm安装
rocminfo

# 检查GPU可见性
HIP_VISIBLE_DEVICES=0 python -c "import torch; print(torch.cuda.is_available())"
```

### 10.3 模型加载问题

如果大语言模型加载失败：

```bash
# 检查Ollama服务状态
sudo systemctl status ollama

# 尝试使用小一点的模型
ollama pull deepseek-coder:6.7b
```

然后更新`config.ini`中的模型配置。

### 10.4 服务无法访问

如果无法访问服务：

```bash
# 检查服务状态
sudo systemctl status rag-system

# 检查网络连接
sudo netstat -tulpn | grep 8089

# 检查防火墙设置
sudo firewall-cmd --list-all
```

## 11. 使用指南

部署完成后，系统应该可以通过以下方式访问：

- 直接访问: http://your_server_ip:8089
- 如果配置了Nginx: http://your_domain_or_ip

初始使用流程：

1. 上传文档到知识库
2. 等待处理完成
3. 开始提问
4. 可以尝试应急管理相关查询（如"查询西兴街道火灾事件5公里范围内的应急资源"）

## 12. 性能优化建议

1. **模型选择**：如果32B模型太大，可以换用更小的模型
2. **嵌入模型调整**：考虑使用较小的嵌入模型，如`BAAI/bge-base-zh-v1.5`
3. **向量数据库优化**：根据文档量选择适合的向量数据库
4. **分块参数调整**：根据具体文档类型调整文本分块大小

## 13. 安全建议

1. 配置SSL证书，启用HTTPS
2. 添加身份验证机制
3. 定期更新系统和依赖包
4. 保护敏感配置信息（如API令牌）
5. 定期检查日志文件，监控异常访问 