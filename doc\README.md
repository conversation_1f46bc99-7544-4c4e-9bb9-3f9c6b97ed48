# RAG 知识库问答系统

## 项目概述

本项目是一个基于 RAG (Retrieval-Augmented Generation) 技术的知识库问答系统，允许用户上传文档，然后基于这些文档进行问答。系统支持多种文档格式，包括 PDF、Word、TXT 等，并使用大语言模型结合文档内容生成回答。此外，系统还集成了LangChain代理功能，可以进行应急管理和资源调度方面的智能问答。系统内置敏感词过滤功能，可以自动识别和过滤敏感内容。

## 系统架构

### 主要组件

1. **前端界面**：提供用户交互界面，包括文档上传和问答功能
2. **文档处理引擎**：处理上传的文档，提取文本内容
3. **向量数据库**：存储文档的向量表示，用于相似度检索
4. **大语言模型**：基于检索到的相关文档内容生成回答
5. **LangChain代理**：提供智能工具调用能力，支持应急管理和资源调度相关查询
6. **敏感词过滤模块**：自动识别和过滤敏感内容

### 技术栈

- **后端**：FastAPI (Python)
- **前端**：HTML, CSS, JavaScript
- **文档处理**：LangChain, PyMuPDF, Tesseract OCR, LibreOffice
- **向量数据库**：Chroma, Qdrant
- **嵌入模型**：HuggingFace Embeddings (bge-large-zh-v1.5), Ollama Embeddings
- **大语言模型**：Ollama (deepseek-r1:32b), OpenAI API
- **代理框架**：LangChain Agents
- **敏感词过滤**：自定义敏感词过滤模块

## 目录结构 
├── config.ini # 配置文件
├── main.py # 主应用入口
├── privateGPT_res.py # RAG 检索和生成逻辑
├── langchain_agent.py # LangChain代理实现
├── api_function_calling.py # API函数调用实现
├── server.js # Node.js服务器
├── log/ # 日志目录
├── static/ # 静态资源
│ └── index_RAG.html # 前端页面
├── sensitive_filter/ # 敏感词过滤模块
│ ├── __init__.py # 敏感词过滤模块初始化
│ ├── sensitive_words.py # 敏感词过滤实现
│ └── sensitive_words.json # 敏感词配置文件
├── doc/ # 文档目录
│ ├── README.md # 项目说明文档
│ ├── api_document.md # API文档
│ └── docker_deployment.md # Docker部署说明
└── src/ # 核心功能模块
    ├── ingest_600.py # 文档摄取和处理
    ├── db_DOC_basic_600/ # 向量数据库存储目录
    └── source_documents/ # 源文档存储目录

## 功能说明

### 1. 文档上传与处理

系统支持上传多种格式的文档，包括：
- PDF 文件 (.pdf)
- Word 文档 (.doc, .docx)
- 文本文件 (.txt)
- Excel 文件 (.csv)
- PowerPoint 文件 (.ppt, .pptx)
- HTML 文件 (.html)
- Markdown 文件 (.md)
- 电子邮件 (.eml)
- 电子书 (.epub)

上传的文档会经过以下处理流程：
1. 文件保存到临时目录
2. 使用适当的加载器提取文本内容
3. 对于扫描版 PDF，使用 OCR 技术提取文本
4. 将文本分割成适当大小的块
5. 计算文本块的向量表示
6. 将向量存储到 Chroma 或 Qdrant 数据库中

### 2. 知识库问答

用户可以通过界面向系统提问，系统会：
1. 将问题转换为向量表示
2. 在向量数据库中检索相关的文本块
3. 将检索到的文本块与问题一起发送给大语言模型
4. 大语言模型生成回答并返回给用户

系统支持会话历史记录，可以进行多轮对话，并且会在回答中标明信息来源。

### 3. 敏感词过滤

系统内置敏感词过滤功能：
1. 自动检测用户输入中的敏感词
2. 敏感词分为两个级别：高度敏感（政治敏感词）和中度敏感（不文明用语）
3. 根据敏感词级别返回相应的提示信息：
   - 高度敏感词：返回"我无法提供相关信息。如果你有其他的问题，我会很乐意为你解答。"
   - 中度敏感词：返回"请使用文明用语。如果你有其他的问题，我会很乐意为你解答。"
4. 防止敏感内容在系统中传播
5. 支持自定义敏感词配置，通过`sensitive_filter/sensitive_words.json`文件进行配置

敏感词过滤实现逻辑：
- 使用`SensitiveWordFilter`类实现敏感词检测
- 支持从JSON配置文件加载敏感词列表
- 采用单例模式确保全局只有一个过滤器实例
- 接口函数包括`check_sensitive_words`和`get_response_for_sensitive_query`

此外，在`langchain_agent.py`中也实现了输出过滤功能，通过`OutputFilterCallbackHandler`自定义回调处理器过滤模型输出中的`<think>...</think>`标签及其内容，确保用户只看到经过处理的、干净的响应内容。

### 4. 应急管理智能代理

系统集成了基于LangChain的智能代理，专门用于处理应急管理和资源调度相关查询：

#### 核心功能

1. **事件周边资源查询**：查询指定事件周围一定距离内的应急资源
   - 示例：`西兴街道火灾事件5公里范围内的应急资源`
   - 支持按资源类型分组展示结果，包括救援队伍、应急仓库、医疗卫生、应急专家等

2. **摄像头预览**：获取指定摄像头的预览地址
   - 示例：`预览摄像头camera001`
   - 开启摄像头预览并返回操作结果

3. **资源位置查询**：查询指定资源的地理位置信息
   - 示例：`查询资源resource001的位置`
   - 返回资源的经纬度坐标

4. **预案管理**：
   - 启动事件预案：`启动西兴街道火灾事件的消防预案`
   - 终止事件预案：`终止西兴街道火灾事件的消防预案`
   - 搜索应急预案：`搜索消防类型的应急预案`

5. **仓库信息查询**：获取物资仓库详细信息
   - 示例：`查询应急物资仓库的信息`
   - 返回仓库名称、地址、联系人及物资列表

6. **事件信息查询**：查询系统中的事件信息
   - 示例：`查询所有火灾类型的处理中事件`
   - 支持按事件类型、状态、时间范围等筛选

#### 技术实现

LangChain Agent实现了以下核心功能：

1. **智能参数提取**：
   - 对于简单格式的查询，系统会通过正则表达式自动提取关键参数（如事件名称、距离、摄像头ID等）
   - 使用`extract_event_params`、`extract_camera_params`、`extract_resource_params`等专用函数进行参数解析
   - 这种方式可以减少对LLM的调用，提高响应速度和准确性

2. **API集成与调用**：
   - 系统集成了多个应急管理相关的API，包括查询事件周边资源、预览摄像头、启动/终止预案等
   - 通过`load_api_config`函数从配置文件加载API信息，支持配置不同类型的API端点
   - 使用`get_api_url`函数动态构建完整的API URL

3. **ReAct代理实现**：
   - 针对复杂查询使用LangChain的ReAct代理模式
   - 自定义`REACT_CUSTOM_PROMPT`模板，优化代理的推理过程
   - 实现`OutputFilterCallbackHandler`回调处理器，过滤代理输出中的思考过程

4. **工具定义与注册**：
   - 使用LangChain的`Tool`类定义了多个工具，每个工具对应一个API功能
   - 工具包括：
     - `query_event_surround_resource_tool`：查询事件周边资源
     - `preview_camera_tool`：预览摄像头
     - `one_map_position_resource_tool`：一张图定位资源
     - `event_stop_plan_tool`：终止事件预案
     - `event_start_plan_tool`：启动事件预案
     - `query_repository_tool`：查询仓库信息
     - `query_all_event_infos_tool`：查询所有事件信息
     - `search_emergency_plan_tool`：搜索应急预案
   - 每个工具都有清晰的描述和示例，帮助模型理解如何使用
   - 通过`create_react_agent`和`AgentExecutor`将工具与代理关联

5. **结果格式化处理**：
   - 使用`format_agent_response`函数处理代理返回的原始输出
   - 移除不必要的思考过程、前缀和标签，使最终输出简洁明了
   - 对不同类型的结果（如资源列表、预案操作结果）进行专门的格式化处理

6. **模型输出过滤**：
   - 实现了`filter_think_tags`函数和`OutputFilterCallbackHandler`回调处理器
   - 过滤模型输出中的`<think>...</think>`标签及其内容
   - 确保返回给用户的内容清晰、简洁，不包含模型的内部思考过程
   - 支持处理不同类型的模型输出（text和message类型）

#### 代理处理流程

1. 接收用户自然语言查询
2. 通过关键词判断是否需要调用API
3. 尝试直接提取查询中的关键参数
4. 如果能成功提取参数，直接调用相应API
5. 如果无法直接提取或查询复杂，创建LangChain ReAct代理
6. 代理分析问题并选择合适的工具
7. 执行工具调用并获取结果
8. 格式化处理结果并返回给用户

#### API调用触发详细流程

系统处理用户问题时，会通过一套智能的多层级判断机制来决定是否需要调用API，就像一个聪明的接线员，知道什么时候该转接到专业部门：

1. 组合关键词识别阶段：
    系统首先检查用户输入是否包含预定义的组合关键词
    程序扫描用户输入，判断是否含有API触发关键词，包含多个关键词组，涵盖资源查询、摄像头操作、预案管理、仓库查询等不同功能域
    只有当输入中包含这些组合关键词时，才会进入下一阶段处理
   避免了不必要的API调用尝试，节省系统资源

2. 正则表达式参数提取阶段：
    当确认包含API触发关键词后，系统尝试通过正则表达式直接提取所需参数
    根据不同的功能域选择相应的参数提取函数
    每个提取函数包含多种匹配模式，以适应用多种不同表达方式
    提取成功后，系统会立即构建API请求参数并直接调用相应API
    这种处理方式避免了大语言模型的调用，可以提高系统响应速度

3. LLM参数提取与判断阶段：
    当正则表达式无法成功提取参数时（如用户表达方式特殊或查询复杂），系统会激活LLM框架的ReAct代理
    代理接收完整的用户查询，通过LLM推理理解用户意图并提取隐含参数
    LLM会分析查询上下文、识别实体间关系，并决定调用哪个具体工具
    系统将LLM的推理过程包装在`<think></think>`标签中，最终呈现给用户的只有处理结果
    确保即使在正则表达式无法处理的复杂查询情况下，系统仍能准确响应

4. 参数验证与默认值处理：
    无论通过正则表达式还是LLM提取的参数，系统都会进行有效性验证
    对于缺失但非必要的参数，系统会应用预设的默认值（如未指定查询距离时默认为5公里）
    对于必要参数缺失的情况，系统会生成提示，引导用户提供更完整的信息

5. API调用结果处理与格式化：
    系统接收API返回的原始数据后，会根据不同的查询类型进行专门的结果格式化处理
    对于资源查询，结果会按资源类型分组并格式化为易读的列表
    对于预案操作，会生成操作成功或失败的确认信息

这套处理机制就像一个智能客服系统：先通过关键词判断需求类型，尝试快速处理简单问题，遇到复杂问题时启动AI助手深入分析，确保信息完整后再处理，最后将结果整理成易懂的形式呈现给用户。这样既保证了处理速度，又能应对各种复杂的自然语言表达，提供准确的服务响应。

### 添加新API工具的步骤

如果需要添加新的API工具功能，需要按照以下步骤进行：

1. **在`src/agent/api_tools.py`文件中添加API函数实现**
   - 添加用于调用新API的函数，如`def new_api_function(input_param: str) -> str:`
   - 实现API的请求构建、调用和响应处理逻辑
   - 添加清晰的函数文档字符串，说明函数功能、参数格式和示例

2. **在`src/agent/param_extractors.py`文件中添加参数提取函数**
   - 添加用于从用户输入中提取参数的函数，如`def extract_new_params(user_input: str) -> Dict[str, Any]:`
   - 实现针对此API的参数提取逻辑，包括正则表达式匹配和参数清理等
   - 确保函数能处理多种表达方式并返回标准格式的参数字典

3. **在`src/agent/__init__.py`文件中导出新函数**
   - 在`api_tools`导入列表中添加新API函数
   - 在`param_extractors`导入列表中添加新参数提取函数
   - 在`__all__`列表中添加两个新函数的名称

4. **在`src/agent/agent_service.py`文件中导入新函数**
   - 更新`api_tools`导入列表，添加新的API函数
   - 更新`param_extractors`导入列表，添加新的参数提取函数

5. **在`src/agent/agent_service.py`的`function_calling_agent`函数中添加关键词检测**
   - 在`keyword_combinations`列表中添加新的关键词组合，用于触发API调用
   - 例如：`["关键词1", "关键词2"]`

6. **在`src/agent/agent_service.py`中添加专门的处理代码块**
   - 添加一个新的条件块检查该特定API的调用条件
   - 添加新API的关键词列表
   - 使用参数提取函数获取API所需参数
   - 调用API函数并处理结果

7. **在`src/agent/agent_core.py`文件中添加工具定义（如需要LLM代理调用）**
   - 添加新的`Tool`定义
   - 为工具提供清晰的名称、描述和示例
   - 将工具添加到`create_agent`函数的工具列表中

8. **在必要时修改`format_agent_response`函数，以支持新的API响应格式化**
   - 如果新API返回的数据格式特殊，需要添加专门的格式化处理逻辑

上述步骤确保了新API工具能够被系统正确识别、调用和处理，同时保持了良好的代码组织结构。系统将能够从用户自然语言输入中识别何时需要调用这个新API，并提取所需参数进行调用。

#### 如果要删除一个接口，应该怎么做（以查找仓库为例），增加接口也要增加下面的5部分。
1.工具定义部分：
    - 移除工具定义：`query_repository_tool`
    query_repository_tool = Tool(
    name="query_repository",
    description="查询仓库信息,输入格式: 仓库名称,页码(默认1),页尺(默认50),例如: 大米仓库或大米仓库,1,50",
    func=query_repository
)
2.函数实现部分：
    - 移除函数实现：`query_repository`
    def query_repository(input_param: str) -> str:
    """查询仓库信息..."""
    
3.参数提取函数：
    - 移除参数提取函数：`extract_repository_params`
    def extract_repository_params(user_input: str) -> Dict[str, Any]:
    """从用户输入中提取仓库名称参数..."""
    
4.工具列表中的引用：
    - 移除工具列表中的引用：`tools_list`
    tools = [
    query_event_surround_resource_tool,
    preview_camera_tool,
    one_map_position_resource_tool,
    event_stop_plan_tool,
    event_start_plan_tool,
    query_repository_tool,  # 需要移除这一行
    query_all_event_infos_tool,
    search_emergency_plan_tool
]
5.在 function_calling_agent 函数中的仓库查询相关代码块：
    - 移除仓库查询相关代码块：
      1）判断是否是普通问答，是否需要调用API的查询（api引入部分）
      # # 6. 查询仓库信息
        # ["查", "仓库"],
        # ["搜", "仓库"],
        # ["大米", "仓库"],
        # ["查", "储备库"],
        # ["查", "物资储备库"]
      2）# 5.检查是否是查询仓库信息请求
      is_repository_query = False
      if "仓库" in user_input:
      3）额外检查
         # 额外检查特殊情况 - 匹配"XX仓库"模式
         if not is_api_query and re.search(r'[\u4e00-\u9fa5]+仓库', user_input) is not None:
            logger.info("检测到XX仓库模式，设置为API查询")
            is_api_query = True
    
6.返回格式化format_agent_response(response):
   查找此部分有没有查找仓库的针对性代码。

#### 精确的参数提取能力

系统实现了一系列专用的参数提取函数，用于从用户的自然语言查询中精确提取参数：

1. **事件和距离提取**：
   - `extract_event_params` 函数使用多种正则模式识别事件名称和查询距离
   - 支持多种表达方式，如"西兴街道火灾事件5公里范围内"或"洪涝灾害事件附近10公里"等

2. **摄像头ID提取**：
   - `extract_camera_params` 函数能识别各种形式的摄像头ID表达
   - 支持"预览摄像头XX"、"查看监控XX"、"camera XX"等多种表达方式

3. **资源名称提取**：
   - `extract_resource_params` 函数专门解析资源定位请求
   - 能从"一张图查看防汛抗旱物资储备库"等复杂表达中提取资源名称

4. **预案类型提取**：
   - `extract_plan_params` 函数识别预案类型关键词
   - 支持"搜索森林火灾预案"等查询表达

5. **仓库名称提取**：
   - `extract_repository_params` 函数精确识别仓库名称
   - 可以处理"查询大米仓库"、"应急物资仓库的信息"等多种表达

6. **事件查询参数提取**：
   - `extract_event_query_params` 函数提取事件名称、状态、等级等参数
   - 支持复杂的事件筛选条件表达

这些函数使用了先进的正则表达式技术，结合业务规则，确保在无需调用大语言模型的情况下快速、准确地提取关键参数，提高了系统响应速度和准确性。

#### 日志记录

系统实现了详细的日志记录功能：
- 记录API调用详情，包括请求参数和响应结果
- 记录参数提取过程，帮助调试和优化提取规则
- 记录代理决策过程，便于分析代理性能
- 使用文件和控制台两种日志输出方式
- 所有日志保存在`log/langchain_agent.log`文件中，便于系统运行分析和问题排查

## 配置说明

系统通过 `config.ini` 文件进行配置，主要配置项包括：

### PATHS 部分
- `LIBREOFFICE_PATH`：LibreOffice 可执行文件路径，用于处理 Office 文档
- `TESSERACT_PATH`：Tesseract OCR 可执行文件路径，用于处理扫描版 PDF

### DATABASE 部分
- `PERSIST_DIRECTORY`：向量数据库存储目录
- `VECTOR_DB_TYPE`：向量数据库类型（chroma 或 qdrant）

### DOCUMENTS 部分
- `SOURCE_DIRECTORY`：源文档存储目录

### EMBEDDINGS 部分
- `MODEL_NAME`：嵌入模型路径
- `EMBEDDING_TYPE`：嵌入模型类型（huggingface 或 ollama）
- `BASE_URL`：Ollama API基础URL
- `OLLAMA_MODEL`：Ollama模型名称

### CHUNKING 部分
- `CHUNK_SIZE`：文本分块大小
- `CHUNK_OVERLAP`：文本分块重叠大小

### LLM 部分
- `LLM_TYPE`：大语言模型类型（ollama 或 openai）
- `MODEL`：大语言模型名称
- `TARGET_SOURCE_CHUNKS`：检索时返回的文档块数量
- `OPENAI_API_KEY`：OpenAI API密钥
- `OPENAI_API_BASE`：OpenAI API基础URL
- `OLLAMA_BASE_URL`：Ollama服务基础URL

### OCR 部分
- `LANGUAGES`：OCR 语言设置

### QDRANT 部分
- `HOST`：Qdrant 服务器主机
- `PORT`：Qdrant 服务器端口
- `COLLECTION_NAME_STORE`：存储集合名称
- `COLLECTION_NAME_QUERY`：查询集合名称

### API 部分
- `BASE_URL`：应急管理API接口基础URL
- `DEFAULT_PATH`：默认API路径
- `REPOSITORY_PATH`：仓库API路径
- `PLAN_PATH`：预案API路径
- `CONTENT_TYPE`：内容类型
- `TOKEN`：API访问令牌

## 环境要求

- Python 3.8+
- LibreOffice (处理 Office 文档)
- Tesseract OCR (处理扫描版 PDF)
- CUDA 支持 (可选，用于加速嵌入计算)
- Ollama (运行本地大语言模型)
- Qdrant (可选，作为向量数据库)

## 部署说明

### Windows 环境

1. 安装 Python 依赖：
   ```
   pip install -r requirements.txt
   ```

2. 安装 LibreOffice 和 Tesseract OCR，并在 `config.ini` 中设置正确的路径

3. 启动应用：
   ```
   uvicorn main:app --host 0.0.0.0 --port 8089 --reload
   ```

### Linux 环境

1. 安装 Python 依赖：
   ```
   pip install -r requirements.txt
   ```

2. 安装 LibreOffice 和 Tesseract OCR：
   ```
   apt-get update
   apt-get install -y libreoffice tesseract-ocr tesseract-ocr-chi-sim
   ```

3. 修改 `config.ini` 中的路径设置：
   ```
   LIBREOFFICE_PATH = /usr/bin/soffice
   TESSERACT_PATH = /usr/bin
   ```

4. 启动应用：
   ```
   uvicorn main:app --host 0.0.0.0 --port 8089
   ```

## 常见问题

1. **文档处理失败**：
   - 检查 LibreOffice 和 Tesseract OCR 是否正确安装
   - 查看日志文件了解详细错误信息

2. **向量计算错误**：
   - 可能是 PyTorch 版本不兼容，尝试重新安装兼容版本
   - 检查 CUDA 是否正确配置（如果使用 GPU）

3. **大语言模型响应慢**：
   - 检查 Ollama 服务是否正常运行
   - 考虑使用更小的模型或增加硬件资源

4. **API连接问题**：
   - 确认API服务器地址和端口配置正确
   - 检查API令牌是否有效
   - 查看日志中的API调用失败详情
