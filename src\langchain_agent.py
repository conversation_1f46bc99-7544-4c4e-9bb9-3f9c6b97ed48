from typing import Optional, Dict, Any, List
import os
import json
import re
import requests
import urllib3
import configparser
from langchain_community.chat_models import ChatOllama
from langchain_openai import ChatOpenAI
from langchain.tools import Tool
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import PromptTemplate
from langchain_core.callbacks.base import BaseCallbackHandler
import logging

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 创建logger
logger = logging.getLogger("langchain_agent")
logger.setLevel(logging.INFO)

# 创建一个handler，用于写入日志文件
if not os.path.exists("log"):
    os.makedirs("log")
log_file = os.path.join("log", "langchain_agent.log")
file_handler = logging.FileHandler(log_file, encoding='utf-8')
file_handler.setLevel(logging.INFO)

# 创建一个handler，用于输出到控制台
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# 定义handler的输出格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# 给logger添加handler
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# 创建自定义的ReAct提示词模板
REACT_CUSTOM_PROMPT = """你是一个专门解答应急管理和资源调度问题的智能助手。请根据用户的问题，直接调用合适的工具并返回结果。

Question: {input}

可用工具:
{tools}

请按以下格式回复：
Action: 要调用的工具名称（必须是下面这些之一: {tool_names}）
Action Input: 输入参数

Observation: 工具返回的结果

Final Answer: 直接返回格式化后的结果，无需额外解释

{agent_scratchpad}"""

# 加载API配置
def load_api_config():
    """
    从config.ini加载API配置信息
    """
    try:
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 检查是否存在API部分
        if 'API' not in config:
            logger.error("配置文件中缺少[API]部分")
            return None
        
        # 加载基础URL
        base_url = config.get('API', 'BASE_URL', fallback='')
        if not base_url:
            logger.error("缺少API基础URL配置")
            return None
        
        # 读取路径配置
        default_path = config.get('API', 'DEFAULT_PATH', fallback='')
        repository_path = config.get('API', 'REPOSITORY_PATH', fallback='')
        plan_path = config.get('API', 'PLAN_PATH', fallback='')
        
        # 构建路径配置字典
        endpoint_paths = {
            "default": default_path,
            "repository": repository_path,
            "plan": plan_path
        }
        
        api_config = {
            'base_url': base_url,
            'endpoint_paths': endpoint_paths,
            'content_type': config.get('API', 'CONTENT_TYPE', fallback=''),
            'token': config.get('API', 'TOKEN', fallback='')
        }
        
        # 验证必要的配置项
        if not api_config['token']:
            logger.error("缺少API访问令牌配置")
            return None
        
        return api_config
    except Exception as e:
        logger.error(f"加载API配置失败: {str(e)}")
        return None

# 获取特定API的完整URL
def get_api_url(api_type="default", endpoint=""):
    """
    获取特定API的完整URL
    :param api_type: API类型，对应配置文件中的路径键
    :param endpoint: API端点路径
    :return: 完整的API URL
    """
    api_config = load_api_config()
    if not api_config:
        logger.error("无法加载API配置")
        return None
    
    base_url = api_config["base_url"]
    endpoint_paths = api_config["endpoint_paths"]
    
    # 获取API路径，如果不存在则使用默认路径
    if api_type in endpoint_paths and endpoint_paths[api_type]:
        path = endpoint_paths[api_type]
    else:
        logger.warning(f"未找到API类型 {api_type} 的路径配置，使用默认路径")
        path = endpoint_paths.get("default", "")
    
    # 确保路径以斜杠开头
    if path and not path.startswith("/"):
        path = f"/{path}"
    
    # 确保路径以斜杠结尾
    if path and not path.endswith("/"):
        path = f"{path}/"
    
    # 构建完整URL
    if endpoint:
        # 确保endpoint不以斜杠开头
        if endpoint.startswith("/"):
            endpoint = endpoint[1:]
        return f"{base_url}{path}{endpoint}"
    else:
        return f"{base_url}{path}"

# 初始化LLM
def initialize_llm(config):
    """
    根据配置初始化LLM模型
    """
    llm_type = config.get("LLM", "LLM_TYPE", fallback="ollama")  # 获取LLM类型
    model_name = config.get("LLM", "MODEL", fallback="deepseek-r1:32b")  # 获取模型名称
    
    # 根据配置选择LLM类型
    if llm_type.lower() == "openai":
        # 使用OpenAI API
        openai_api_key = config.get("LLM", "OPENAI_API_KEY", fallback="")
        openai_api_base = config.get("LLM", "OPENAI_API_BASE", fallback="https://api.openai.com/v1")
        
        print(f"使用OpenAI模型: {model_name}, API基础URL: {openai_api_base}")
        llm = ChatOpenAI(
            model_name=model_name,
            openai_api_key=openai_api_key,
            openai_api_base=openai_api_base,
        )
    else:
        # 默认使用Ollama
        ollama_base_url = config.get("LLM", "OLLAMA_BASE_URL", fallback="http://localhost:11434")
        
        print(f"使用Ollama模型: {model_name}, 基础URL: {ollama_base_url}")
        llm = ChatOllama(
            model=model_name, 
            base_url=ollama_base_url
        )
    
    return llm

# 过滤模型输出中的<think>标签和内容
def filter_think_tags(text):
    """
    过滤掉模型输出中的<think>...</think>标签及其内容
    """
    if not text:
        return text
    filtered_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    return filtered_text

# 创建一个自定义回调处理器来过滤输出
class OutputFilterCallbackHandler(BaseCallbackHandler):
    """过滤模型输出的回调处理器"""
    
    def on_llm_new_token(self, token: str, **kwargs) -> None:
        """流式输出时过滤token"""
        pass
    
    def on_llm_end(self, response, **kwargs) -> None:
        """当LLM完成生成时过滤输出"""
        if hasattr(response, 'generations') and response.generations:
            for gen_list in response.generations:
                for gen in gen_list:
                    if hasattr(gen, 'text'):
                        gen.text = filter_think_tags(gen.text)
                    # 处理message类型的输出
                    if hasattr(gen, 'message') and hasattr(gen.message, 'content'):
                        gen.message.content = filter_think_tags(gen.message.content)
    
    def on_chain_end(self, outputs, **kwargs) -> None:
        """当Chain完成时过滤输出"""
        if 'output' in outputs and isinstance(outputs['output'], str):
            outputs['output'] = filter_think_tags(outputs['output'])

# API调用函数
def query_event_surround_resource(input_param: str) -> str:
    """
    查询事件周边资源
    输入参数格式: 事件名称,距离,资源类型(可选)
    例如：西兴街道火灾事件,5,救援队伍
    """
    try:
        # 解析参数
        params = input_param.split(',')
        if len(params) < 2:
            return "参数不足，请提供事件名称和查询距离"
        
        event_name = params[0].strip()
        if not event_name:
            return "事件名称不能为空"
        
        try:
            distance = int(params[1].strip())
        except ValueError:
            return "距离必须是整数"
        
        resource_type = ""
        if len(params) > 2:
            resource_type = params[2].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API - 使用get_api_url函数获取完整URL
        api_url = get_api_url("default", "queryEventSurroundResource")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "eventName": event_name,
            "arroundDistance": distance,
            "resourceType": resource_type
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询事件周边资源API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            resources = response_data.get("data", [])
            count = len(resources)
            
            if not resources:
                if resource_type:
                    return f"{event_name}{distance}公里范围内未找到{resource_type}资源"
                else:
                    return f"{event_name}{distance}公里范围内未找到应急资源"
            else:
                # 按类型分组
                resource_groups = {}
                for resource in resources:
                    res_type = resource.get("type", "other")
                    if res_type not in resource_groups:
                        resource_groups[res_type] = []
                    resource_groups[res_type].append(resource)
                
                # 类型映射表（英文到中文）
                type_mapping = {
                    "rescue_team": "救援队伍",
                    "repository": "物资仓库",
                    "medical_health": "医疗健康机构",
                    "protection_target": "保护目标",
                    "emergency_expert": "应急专家",
                    "material": "应急物资",
                    "equipment": "应急设备",
                    "common": "通用设施",
                    "enterpriseInfo": "企业信息",
                    "other": "其他资源"
                }
                
                # 格式化输出
                result = [f"{event_name}{distance}公里范围内的应急资源包括："]
                
                # 按类型处理资源
                for res_type in ["rescue_team", "repository", "medical_health", "emergency_expert", 
                                "material", "equipment", "protection_target", "enterpriseInfo", 
                                "common", "other"]:
                    if res_type in resource_groups and resource_groups[res_type]:
                        type_name = type_mapping.get(res_type, res_type)
                        result.append(f"\n{type_name}:")
                        
                        for item in resource_groups[res_type]:
                            name = item.get("name", "未知")
                            item_distance = item.get("distance", "未知")
                            phone = item.get("phoneNumber", "")
                            
                            resource_info = f"- {name}"
                            if item_distance:
                                resource_info += f"，距离{item_distance}公里"
                            if phone:
                                resource_info += f"，联系电话{phone}"
                            
                            result.append(resource_info)
                
                return "\n".join(result)
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询事件周边资源错误: {str(e)}")
        return f"处理查询时出错: {str(e)}"

def preview_camera(input_param: str) -> str:
    """
    预览摄像头
    输入参数格式: 摄像头ID
    例如：camera001
    """
    try:
        # 解析参数
        camera_id = input_param.strip()
        if not camera_id:
            return "摄像头ID不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "oneMapPreviewCameraByName")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "cameraName": camera_id
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用预览摄像头API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            camera_data = response_data.get("data", {})
            camera_url = camera_data.get("url", "")
            
            if camera_url:
                # 返回开启摄像头预览成功的消息
                return f"已成功开启 {camera_id} 摄像头预览"
            elif camera_data.get("resultMsg"):
                # 如果有resultMsg字段，直接返回该信息
                return camera_data.get("resultMsg")
            else:
                return f"未能成功开启 {camera_id} 摄像头预览"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"预览失败: {error_msg}"
    except Exception as e:
        logger.error(f"预览摄像头错误: {str(e)}")
        return f"处理预览请求时出错: {str(e)}"

def one_map_position_resource(input_param: str) -> str:
    """
    一张图查看并定位资源
    输入参数格式: 资源名称
    例如：防汛抗旱物资储备库
    """
    try:
        # 解析参数
        resource_name = input_param.strip()
        if not resource_name:
            return "资源名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "oneMapPositionResourceByName")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "resourceName": resource_name,
            "resourceType": ""
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询资源位置API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            position_data = response_data.get("data", {})
            # 如果有resultMsg字段，直接返回该信息
            if position_data.get("resultMsg"):
                return position_data.get("resultMsg")
            # 否则返回默认成功信息
            return f"【{resource_name}】成功定位！"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询资源位置错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def event_stop_plan(input_param: str) -> str:
    """
    终止事件预案
    输入格式: 事件名称,预案名称
    例如: 西兴街道火灾事件,消防预案
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        event_name = params[0].strip()
        plan_name = params[1].strip()
        
        if not event_name:
            return "事件名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "stopEventPlan")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "eventName": event_name,
            "planName": plan_name
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用终止事件预案API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            return f"已成功终止事件{event_name}的{plan_name}预案"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"终止失败: {error_msg}"
    except Exception as e:
        logger.error(f"终止事件预案错误: {str(e)}")
        return f"处理终止请求时出错: {str(e)}"

def event_start_plan(input_param: str) -> str:
    """
    启动事件预案
    输入格式: 事件名称,预案名称
    例如: 西兴街道火灾事件,消防预案
    """
    try:
        # 解析参数
        params = input_param.split(',')
        # if len(params) < 2:
        #     return "参数不足，请提供事件名称和预案名称"
        
        event_name = params[0].strip()
        plan_name = params[1].strip()
        
        if not event_name:
            return "事件名称不能为空"
        
        # if not plan_name:
        #     return "预案名称不能为空"
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "startEventPlan")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "eventName": event_name,
            "planName": plan_name
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用启动事件预案API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            return f"已成功启动事件{event_name}的{plan_name}预案"
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"启动失败: {error_msg}"
    except Exception as e:
        logger.error(f"启动事件预案错误: {str(e)}")
        return f"处理启动请求时出错: {str(e)}"

def query_repository(input_param: str) -> str:
    """
    查询仓库信息
    输入格式: 仓库名称,页码(可选,默认1),页尺(可选,默认50)
    例如: 大米仓库 或 大米仓库,1,50
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        repository_name = params[0].strip()
        if not repository_name:
            return "仓库名称不能为空"
        
        # 页码和页尺默认值
        page_num = "1"
        page_size = "50"
        
        # 如果提供了页码和页尺，则使用提供的值
        if len(params) > 1 and params[1].strip():
            page_num = params[1].strip()
        
        if len(params) > 2 and params[2].strip():
            page_size = params[2].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API - 使用repository类型的API URL
        api_url = get_api_url("repository", "fetchRepositoryByCondition")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "repositoryName": repository_name,
            "pageNo": page_num,
            "pageSize": page_size
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询仓库信息API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            data = response_data.get("data", {})
            if not data:
                return f"未找到仓库信息"
                
            rows = data.get("rows", [])
            
            if not rows:
                return f"未找到名为 {repository_name} 的仓库信息"
            
            result = []
            
            for repo in rows:
                repo_name = repo.get("repositoryName", "未知")
                longitude = repo.get("longitude", "未知")
                latitude = repo.get("latitude", "未知")
                belong_unit = repo.get("belongUnitName", "未知")
                capacity = repo.get("capacity", "未知")
                level = repo.get("repositoryLevelName", "未知")
                
                repo_info = f"{repo_name}位于经纬度{latitude}, {longitude}，属于{belong_unit}，容量为{capacity}，级别为{level}。"
                result.append(repo_info)
            
            return "\n".join(result)
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询仓库信息错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def query_all_event_infos(input_param: str) -> str:
    """
    查询所有事件信息
    输入格式: 页码(可选,默认1),页大小(可选,默认50),事件状态(可选),事件名称(可选),事件等级(可选)
    例如: 1,50,4,暴雨事件,2
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        # 默认参数
        page_no = "1"
        page_size = "150"
        event_status = ""
        event_name = ""
        event_level = ""
        
        # 根据提供的参数个数设置对应的值
        if len(params) > 0 and params[0].strip():
            page_no = params[0].strip()
        
        if len(params) > 1 and params[1].strip():
            page_size = params[1].strip()
        
        if len(params) > 2 and params[2].strip():
            event_status = params[2].strip()
        
        if len(params) > 3 and params[3].strip():
            event_name = params[3].strip()
            
        if len(params) > 4 and params[4].strip():
            event_level = params[4].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API
        api_url = get_api_url("default", "queryAllEventInfos")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "pageNo": page_no,
            "pageSize": page_size,
            "eventStatus": event_status,
            "eventName": event_name,
            "eventLevel": event_level
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用查询所有事件信息API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            data = response_data.get("data", {})
            rows = data.get("rows", [])
            total = data.get("total", 0)
            
            if not rows:
                return "未找到符合条件的事件"
            
            # 格式化输出
            result = [f"当前系统中共有{total}个事件，以下是所有事件的信息：\n"]
            
            for i, event in enumerate(rows, 1):
                event_name = event.get("eventName", "未知事件")
                event_type_name = event.get("eventTypeName", "未知类型")
                event_type_detail_name = event.get("eventTypeDetailName", "未知子类型")
                event_level = event.get("eventLevel", "未知等级")
                event_status = event.get("eventStatus", "未知状态")
                latitude = event.get("latitude", "")
                longitude = event.get("longitude", "")
                
                # 添加事件信息
                result.append(f"{i}. 事件名称：{event_name}")
                result.append(f"- 事件类型：{event_type_name}")
                result.append(f"- 事件子类型：{event_type_detail_name}")
                result.append(f"- 事件等级：{event_level}")
                result.append(f"- 事件状态：{event_status}")
                if latitude and longitude:
                    result.append(f"- 经纬度：{latitude}, {longitude}")
                result.append("")  # 添加空行分隔不同事件
            
            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"查询所有事件信息错误: {str(e)}")
        return f"处理查询请求时出错: {str(e)}"

def search_emergency_plan(input_param: str) -> str:
    """
    搜索应急预案
    输入格式: 预案类型（为空时查所有预案），页码(可选，默认为1),页面大小(可选，默认20)
    例如: 森林火灾,1,20
    """
    try:
        # 解析参数
        params = input_param.split(',')
        
        # 默认参数
        plan_type_name = ""
        page_no = "1"
        page_size = "120"
        
        # 根据提供的参数个数设置对应的值
        if len(params) > 0 and params[0].strip():
            plan_type_name = params[0].strip()
        
        if len(params) > 1 and params[1].strip():
            page_no = params[1].strip()
        
        if len(params) > 2 and params[2].strip():
            page_size = params[2].strip()
        
        # 获取API配置
        api_config = load_api_config()
        if not api_config:
            return "API配置加载失败"
        
        # 调用API - 使用plan类型的API URL
        api_url = get_api_url("plan", "queryPlanInfo")
        if not api_url:
            return "API URL构建失败"
            
        headers = {
            "Content-Type": "application/json",
            "Token": api_config['token']
        }
        
        payload = {
            "planTypeName": plan_type_name,
            "pageNo": page_no,
            "pageSize": page_size
        }
        
        response = requests.post(api_url, json=payload, headers=headers, verify=False)
        
        # 记录API调用信息
        logger.info(f"调用搜索应急预案API: {api_url}")
        logger.info(f"请求参数: {payload}")
        
        # 解析结果
        response_data = response.json()
        logger.info(f"API返回结果: {response_data}")
        
        if response_data.get("code") == "0":
            data = response_data.get("data", {})
            rows = data.get("rows", [])
            total = data.get("total", 0)
            
            if not rows:
                return "未找到符合条件的应急预案"
            
            # 格式化输出
            result = [f"当前系统中共有{total}个预案，以下是所有预案的信息：\n"]
            
            for i, plan in enumerate(rows, 1):
                plan_name = plan.get("planName", "未知预案")
                plan_type = plan.get("planType", "未知类型")
                plan_status = plan.get("examineStatus", "未知状态")
                plan_tag = plan.get("planTagDetailName", "无标签")
                
                # 添加预案信息
                result.append(f"{i}. 预案名称：{plan_name}")
                result.append(f"- 预案类型：{plan_type}")
                result.append(f"- 预案状态：{plan_status}")
                result.append(f"- 预案标签：{plan_tag}")
                result.append("")  # 添加空行分隔不同预案
            
            return "\n".join(result).strip()
        else:
            error_msg = response_data.get("msg", "未知错误")
            return f"查询失败: {error_msg}"
    except Exception as e:
        logger.error(f"搜索应急预案错误: {str(e)}")
        return f"处理搜索请求时出错: {str(e)}"

# 定义LangChain工具
query_event_surround_resource_tool = Tool(
    name="query_event_surround_resource",
    description="查询事件周边的应急资源,输入格式: 事件名称,距离,资源类型(可选),例如：西兴街道火灾事件,5,救援队伍",
    func=query_event_surround_resource
)

preview_camera_tool = Tool(
    name="preview_camera",
    description="预览摄像头,输入格式: 摄像头ID,例如：camera001",
    func=preview_camera
)

one_map_position_resource_tool = Tool(
    name="one_map_position_resource",
    description="一张图查看并定位资源,输入格式: 资源名称,资源类型(为空),例如：防汛抗旱物资储备库,仓库",
    func=one_map_position_resource
)

event_stop_plan_tool = Tool(
    name="event_stop_plan",
    description="终止事件预案,输入格式: 事件名称 例如: 西兴街道火灾事件",
    func=event_stop_plan
)

event_start_plan_tool = Tool(
    name="event_start_plan",
    description="启动事件预案,输入格式: 事件名称,预案名称（为空）,例如: 西兴街道火灾事件",
    func=event_start_plan
)

# query_repository_tool = Tool(
#     name="query_repository",
#     description="查询仓库信息,输入格式: 仓库名称,页码(默认1),页尺(默认50),例如: 大米仓库或大米仓库,1,50",
#     func=query_repository
# )

query_all_event_infos_tool = Tool(
    name="query_all_event_infos",
    description="搜索事件.输入格式: 页码(默认1,类型是int),每页大小(默认50,类型是int),事件状态(可选,默认为空),事件名称(可选,当为空时查所有事件),事件等级(可选,默认为空)。例如: 1,50,4,洪涝灾害事件,1",
    func=query_all_event_infos
)

search_emergency_plan_tool = Tool(
    name="search_emergency_plan",
    description="搜索应急预案,输入格式: 预案类型（为空时查所有预案）,页码(默认为1),页面大小(默认20),例如: 森林火灾,1,20",
    func=search_emergency_plan
)

# 创建代理
def create_agent():
    """
    创建函数调用代理
    """
    try:
        # 加载配置
        config = configparser.ConfigParser()
        config.read("config.ini", encoding='utf-8')
        
        # 初始化LLM
        llm = initialize_llm(config)
        
        # 添加输出过滤回调
        output_filter = OutputFilterCallbackHandler()
        if not hasattr(llm, 'callbacks') or llm.callbacks is None:
            llm.callbacks = [output_filter]
        else:
            llm.callbacks.append(output_filter)
        
        # 加载工具
        tools = [
            query_event_surround_resource_tool,
            preview_camera_tool,
            one_map_position_resource_tool,
            event_stop_plan_tool,
            event_start_plan_tool,
            #query_repository_tool,
            query_all_event_infos_tool,
            search_emergency_plan_tool
        ]
        
        # 使用自定义提示词模板创建代理
        react_prompt = PromptTemplate.from_template(REACT_CUSTOM_PROMPT)
        
        # 使用create_react_agent替代initialize_agent
        agent = create_react_agent(
            llm=llm, 
            tools=tools, 
            prompt=react_prompt
        )
        
        # 创建代理执行器
        agent_executor = AgentExecutor.from_agent_and_tools(
            agent=agent,
            tools=tools,
            verbose=True,
            max_iterations=5,
            handle_parsing_errors=True,
            callbacks=[output_filter],  # 添加输出过滤器
            return_intermediate_steps=False  # 设置为False，只返回最终结果，不返回中间步骤
        )
        
        return agent_executor
    except Exception as e:
        logger.error(f"创建代理失败: {str(e)}")
        return None

# 格式化返回结果
def format_agent_response(response):
    """
    格式化代理的响应
    """
    try:
        # 检查是否为空
        if not response:
            return response

        # 检查是否因迭代限制停止
        if "Agent stopped due to iteration limit" in str(response):
            return "没有找到相应工具或资源，请确认任务名称，并重新输入。"
            
        # 快速路径：如果响应中包含已格式化的资源列表，直接返回
        if "公里范围内的" in response and "：\n" in response:
            # 提取格式化后的资源列表部分
            parts = response.split("：\n")
            if len(parts) > 1:
                return response
        
        # 快速路径：检测启动预案相关的返回结果
        if "已成功启动事件" in response or "成功启动预案" in response or "已成功终止事件" in response or "成功终止预案" in response:
            logger.info("检测到预案启动/终止结果，直接返回")
            return response
        
        # 快速路径：检测成功定位资源的返回结果
        if "成功定位" in response and "【" in response and "】" in response:
            logger.info("检测到资源定位结果，直接返回")
            return response
            
        # 快速路径：检测常见的空结果返回
        if "未找到符合条件的事件" in response or "未找到符合条件的应急预案" in response or "未找到符合条件的" in response:
            logger.info("检测到空结果返回，直接返回")
            return response
        
        # 快速路径：如果是API直接返回的Observation结果
        observation_match = re.search(r'Observation:(.*?)(?:Action:|Final Answer:|$)', response, re.DOTALL)
        if observation_match:
            observation = observation_match.group(1).strip()
            if "公里范围内的" in observation:
                return observation
        
        # 如果有Final Answer且包含格式化内容，直接返回
        final_answer_match = re.search(r'Final Answer:(.*?)$', response, re.DOTALL)
        if final_answer_match:
            final_answer = final_answer_match.group(1).strip()
            if "公里范围内的" in final_answer or "- **" in final_answer:
                return final_answer
        
        # 移除预定义前缀
        prefixes_to_remove = ["Observation:", "Thought:", "Action:", "Action Input:", "Final Answer:"]
        lines = response.strip().split('\n')
        filtered_lines = []
        
        for line in lines:
            if not any(line.strip().startswith(prefix) for prefix in prefixes_to_remove):
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines).strip()
        
    except Exception as e:
        logger.error(f"格式化响应错误: {str(e)}")
        return response

# 提取事件名称和距离参数
def extract_event_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取事件名称和距离参数
    """
    event_name = ""
    distance = 0
    
    # 清除常见的修饰前缀
    cleaned_input = user_input
    prefixes = ["请你告诉我", "请告诉我", "请查询", "查询", "请问", "告诉我", 
               "帮我查一下", "帮我看看", "帮忙查询", "帮忙看看", "麻烦查询", 
               "麻烦告诉我", "我想知道", "能告诉我", "能查询", "能帮我查询",
               "想了解", "查一下", "看一下", "帮我问一下", "帮我确认一下",
               "请帮我查看", "请帮我确认", "请问一下", "麻烦问一下", 
               "麻烦帮我查", "能否告诉我", "能否查询", "能否帮我看", 
               "想请教", "请协助查询", "协助查看", "帮忙问一下", "我想知道","查找"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):]
            break
    
    # 首先处理"请告诉我洪涝灾害事件"这样的格式
    if "请告诉我" in user_input and "事件" in user_input:
        # 尝试直接提取"XX事件"
        event_matches = re.findall(r'([\w\s]+?事件)', user_input)
        if event_matches:
            # 排除带有"请告诉我"的匹配
            for match in event_matches:
                if "请告诉我" not in match:
                    event_name = match
                    break
    
    # 如果上面没有匹配到，尝试其他模式
    if not event_name:
        # 尝试直接匹配可能的事件名称模式
        event_patterns = [
            r'([\w\s]+?事件)',  # 匹配"XX事件"
            r'发生了([\w\s]+?事件)',  # 匹配"发生了XX事件"，优先提取事件部分
            r'发生([\w\s]+?事件)',  # 匹配"发生XX事件"，优先提取事件部分
            r'发生了([\w\s]+?)，',  # 匹配"发生了XX，"
            r'发生([\w\s]+?)，',    # 匹配"发生XX，"
        ]
        
        for pattern in event_patterns:
            event_match = re.search(pattern, cleaned_input)
            if event_match:
                event_name = event_match.group(1).strip()
                # 检查提取出的事件名是否包含"发生"或"发生了"前缀，如果包含则去除
                if event_name.startswith("发生了"):
                    event_name = event_name[3:].strip()
                elif event_name.startswith("发生"):
                    event_name = event_name[2:].strip()
                # 检查是否包含"请告诉我"等前缀
                if event_name.startswith("请告诉我"):
                    event_name = event_name[4:].strip()
                break
    
    # 如果上面的模式没有匹配到，尝试直接提取可能的事件名称
    if not event_name and "事件" in cleaned_input:
        parts = cleaned_input.split("事件")[0].split()
        if parts:
            event_name = parts[-1] + "事件"
            # 检查是否有"告诉我"等前缀
            if "请告诉我" in event_name:
                event_name = event_name.replace("请告诉我", "").strip()
    
    # 特殊处理洪涝灾害事件的提取
    if "洪涝灾害" in cleaned_input and "事件" in cleaned_input:
        event_name = "洪涝灾害事件"
    
    # 清理提取到的事件名称中的标点符号
    if event_name:
        # 移除常见中英文标点符号
        event_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', event_name).strip()
            
    # 尝试匹配 "数字公里"，包括 "附近X公里" 和 "X公里范围" 的格式
    distance_patterns = [
        r'(\d+)[\s]*公里',  # 常规匹配, 例如 "5公里"
        r'附近[\s]*(\d+)[\s]*公里',  # 匹配 "附近10公里"
        r'(\d+)[\s]*公里范围',  # 匹配 "10公里范围"
    ]
    
    for pattern in distance_patterns:
        distance_match = re.search(pattern, cleaned_input)
        if distance_match:
            distance = int(distance_match.group(1))
            break
    
    logger.info(f"提取参数结果：事件名称={event_name}, 距离={distance}公里")
    return {
        "event_name": event_name,
        "distance": distance
    }

# 提取摄像头ID参数
def extract_camera_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取摄像头ID参数
    """
    camera_id = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input.lower()
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", "是否可以", "麻烦", "麻烦你"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
    
    # 优先直接匹配整个输入中的"数字-Camera 数字"这种特定格式
    specific_format_match = re.search(r'(?i)(\d+\s*-\s*camera\s+\d+)', user_input)
    if specific_format_match:
        camera_id = specific_format_match.group(1).strip()
        logger.info(f"直接匹配到特定格式摄像头ID: {camera_id}")
        return {"camera_id": camera_id}
    
    # 尝试匹配常见的摄像头请求模式
    camera_patterns = [
        # 优先匹配"数字-Camera 数字"这种特定格式
        r'(?i)(\d+\s*-\s*camera\s+\d+)',  # 匹配138-Camera 01这种格式
        r'预览(?:摄像头|监控|摄像机|监控点|点位|探头|camera)?\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',  # 预览摄像头XX、预览监控XX等
        r'查看(?:摄像头|监控|摄像机|监控点|点位|探头|camera)?\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',  # 查看摄像头XX等
        r'打开(?:摄像头|监控|摄像机|监控点|点位|探头|camera)?\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',  # 打开摄像头XX等
        r'开启(?:摄像头|监控|摄像机|监控点|点位|探头|camera)?\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',  # 开启摄像头XX等
        r'监控点\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',      # 监控点XX
        r'camera\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',      # camera XX
        r'([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)\s*(?:摄像头|监控|摄像机|点位|探头)的?(?:录像|视频|画面|预览|监控)',  # XX摄像头的预览、XX监控的画面等
        r'(?:摄像头|监控|摄像机|点位|探头)?\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)',  # 摄像头XX，作为最后的兜底模式
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in camera_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            camera_id = match.group(1).strip()
            break
    
    # 如果直接匹配失败，尝试从camera后面提取
    if not camera_id and "camera" in cleaned_input.lower():
        # 提取camera后面的内容
        parts = cleaned_input.lower().split("camera")
        if len(parts) > 1 and parts[1].strip():
            # 提取camera后面的第一个词或数字组合
            cam_match = re.search(r'\s*([a-zA-Z0-9\-]+\s*[a-zA-Z0-9]*)', parts[1])
            if cam_match:
                camera_id = cam_match.group(1).strip()
    
    # 清理提取到的摄像头ID中的标点符号
    if camera_id:
        # 移除常见中英文标点符号，但保留连字符"-"，因为它可能是摄像头ID的一部分
        camera_id = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', camera_id).strip()
    
    # 保留原始大小写
    if camera_id:
        # 尝试从原始输入中找到对应的文本
        original_match = re.search(r'(?i)' + re.escape(camera_id), user_input)
        if original_match:
            camera_id = original_match.group(0).strip()
    
    logger.info(f"提取摄像头参数结果：camera_id={camera_id}")
    return {
        "camera_id": camera_id
    }

# 提取资源名称参数
def extract_resource_params(user_input: str) -> Dict[str, Any]:
    """
    一张图查看资源从用户输入中提取资源名称参数
    """
    resource_name = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input.lower()
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", "是否可以", "麻烦", "麻烦你"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
    
    # 直接匹配特定的完整资源名称
    if "一张图查看" in cleaned_input:
        # 为特定的完整资源名称创建的特殊处理
        complete_names = ["防汛抗旱物资储备库", "应急物资储备中心", "应急避难场所", "应急管理局"]
        for name in complete_names:
            if name in user_input:
                logger.info(f"直接匹配到完整资源名称: {name}")
                return {"resource_name": name}
    
    # 尝试匹配常见的资源查询模式
    resource_patterns = [
        # 特殊处理完整的库名或站点名
        r'一张图查看.*?((?:[\u4e00-\u9fa5]+物资|应急|[\u4e00-\u9fa5]+中心|[\u4e00-\u9fa5]+仓库|[\u4e00-\u9fa5]+储备库|[\u4e00-\u9fa5]+站))',  # 专门匹配"防汛抗旱物资储备库"等完整名称
        r'一张图查看(?:并定位)?\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图查看XX"，使用非贪婪匹配并以句子结束或空格、逗号、句号为界
        r'一张图定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"一张图定位XX"
        r'一张图查看(?:并定位)?\s*([\u4e00-\u9fa5]+[库厂站点门所车])',  # 专门匹配中文资源名称，如"防汛抗旱物资储备库"
        r'定位\s*(.+?)(?:$|\s|，|。)',  # 匹配"定位XX"
        r'查看\s*(.+?)的位置',  # 匹配"查看XX的位置"
        r'查询\s*(.+?)的位置',  # 匹配"查询XX的位置"
        r'(?:查询|查看)\s*(.+?)(?:位置|在哪里|在哪儿|在什么地方)',  # 匹配"查询XX位置"、"查看XX在哪里"等
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in resource_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            resource_name = match.group(1).strip()
            break
    
    # 如果直接匹配失败，尝试从"查看"或"定位"后面提取
    if not resource_name:
        if "查看" in cleaned_input:
            parts = cleaned_input.split("查看")
            if len(parts) > 1 and parts[1].strip():
                resource_name = parts[1].strip()
        elif "定位" in cleaned_input:
            parts = cleaned_input.split("定位")
            if len(parts) > 1 and parts[1].strip():
                resource_name = parts[1].strip()
    
    # 清理提取到的资源名称中的标点符号
    if resource_name:
        # 移除常见中英文标点符号
        resource_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', resource_name).strip()
    
    # 保留原始大小写
    if resource_name:
        # 尝试从原始输入中找到对应的文本
        search_pattern = re.escape(resource_name).replace('\\s+', '\\s*')
        original_match = re.search(search_pattern, user_input, re.IGNORECASE)
        if original_match:
            resource_name = original_match.group(0).strip()
    
    logger.info(f"提取资源参数结果：resource_name={resource_name}")
    return {
        "resource_name": resource_name
    }

# 提取预案参数
def extract_plan_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取预案类型参数
    """
    plan_type = ""
    
    # 清除常见的修饰前缀
    cleaned_input = user_input.lower()
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", "是否可以", "麻烦", "麻烦你"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
    
    # 检查是否请求所有预案
    if "所有预案" in cleaned_input or "全部预案" in cleaned_input:
        logger.info("用户请求查询所有预案")
        return {"plan_type": ""}
    
    # 尝试匹配预案类型
    plan_type_patterns = [
        r'(?:搜索|查询|查找|查看)(\w+(?:火灾|灾害|事故|安全|突发|事件))(?:类型)?(?:的)?(?:应急)?预案',  # 搜索/查询XX火灾预案
        r'(\w+(?:火灾|灾害|事故|安全|突发|事件))(?:类型)?(?:的)?(?:应急)?预案',      # XX火灾预案
        r'(?:应急)?预案(?:类型)?(?:是)?(\w+(?:火灾|灾害|事故|安全|突发|事件))',      # 预案类型是XX火灾
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in plan_type_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            plan_type = match.group(1).strip()
            break
    
    # 如果还没有匹配到，尝试提取关键词
    if not plan_type:
        keywords = ["火灾", "洪水", "地震", "台风", "泥石流", "暴雨", "干旱", "森林火灾", 
                   "交通事故", "危险品", "安全生产", "突发事件"]
        for keyword in keywords:
            if keyword in cleaned_input:
                plan_type = keyword
                break
    
    # 清理提取到的预案类型中的标点符号
    if plan_type:
        # 移除常见中英文标点符号
        plan_type = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', plan_type).strip()
    
    logger.info(f"提取预案参数结果：plan_type={plan_type}")
    return {
        "plan_type": plan_type
    }

# 提取仓库参数
def extract_repository_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取仓库名称参数
    """
    repository_name = ""
    page_num = "1"
    page_size = "50"
    
    # 清除常见的修饰前缀
    cleaned_input = user_input.lower()
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", "是否可以", "麻烦", "麻烦你"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
    
    # 定义仓库名称匹配模式
    repository_patterns = [
        r'([\u4e00-\u9fa5]{1,10})(?:仓库|库)',  # 匹配"XX仓库"或"XX库"
        r'(?:查找|查询|查看|找)(?:下)?([\u4e00-\u9fa5]{1,10})(?:仓库|库)',  # 匹配"查找大米仓库"这样的格式
    ]
    
    # 遍历所有模式尝试匹配
    for pattern in repository_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            repository_name = match.group(1).strip()
            # 排除常见的干扰词
            exclude_words = ["查找下", "查找", "查询", "查看", "给我", "我要"]
            if not any(word in repository_name for word in exclude_words):
                break
    
    # 如果没有匹配到具体名称，检查是否有"物资"关键词
    if not repository_name and ("物资" in cleaned_input or "应急物资" in cleaned_input):
        repository_name = "应急物资" if "应急物资" in cleaned_input else "物资"
    
    # 清理提取到的仓库名称中的标点符号
    if repository_name:
        repository_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', repository_name).strip()
    
    # 尝试匹配页码和页大小
    page_match = re.search(r'第(\d+)页', cleaned_input)
    if page_match:
        page_num = page_match.group(1)
    
    size_match = re.search(r'每页(\d+)条', cleaned_input)
    if size_match:
        page_size = size_match.group(1)
    
    logger.info(f"提取仓库参数结果：repository_name={repository_name}, page_num={page_num}, page_size={page_size}")
    return {
        "repository_name": repository_name,
        "page_num": page_num,
        "page_size": page_size
    }

# 提取事件查询参数
def extract_event_query_params(user_input: str) -> Dict[str, Any]:
    """
    从用户输入中提取事件查询参数
    """
    event_name = ""
    event_status = ""
    event_level = ""
    page_no = "1"
    page_size = "50"
    
    # 清除常见的修饰前缀
    cleaned_input = user_input.lower()
    prefixes = ["请你", "请", "帮我", "我想", "我要", "我希望", "能否", "能不能", "是否可以", "麻烦", "麻烦你"]
    for prefix in prefixes:
        if cleaned_input.startswith(prefix):
            cleaned_input = cleaned_input[len(prefix):].strip()
    
    # 检查是否查询所有事件
    if ("所有事件" in cleaned_input or "全部事件" in cleaned_input) and not event_name:
        logger.info("用户请求查询所有事件")
        return {
            "event_name": "",
            "event_status": "",
            "event_level": "",
            "page_no": page_no,
            "page_size": page_size
        }
    
    # 尝试提取事件名称
    if "事件" in cleaned_input:
        event_patterns = [
            r'(?:事件名称是|事件名称为|事件叫做|事件叫|事件为)?[\s]*([^,，\s]+?事件)',  # 事件名称是XX事件
            r'([^,，\s]+?事件)(?:的信息|的详情|的资料)',  # XX事件的信息
        ]
        
        for pattern in event_patterns:
            match = re.search(pattern, cleaned_input)
            if match:
                event_name = match.group(1).strip()
                break
    
    # 清理提取到的事件名称中的标点符号
    if event_name:
        # 移除常见中英文标点符号
        event_name = re.sub(r'[,.。，、；;：:！!?？""''\'\"()（）【】\[\]{}\s]', '', event_name).strip()
    
    # 提取事件状态
    status_mapping = {
        "处理中": "4",
        "待处理": "3",
        "已归档": "5",
        "已完成": "5"
    }
    
    for status_text, status_code in status_mapping.items():
        if status_text in cleaned_input:
            event_status = status_code
            break
    
    # 提取事件等级
    level_patterns = [
        r'(?:事件等级|等级)(?:是|为)?[\s]*(\d)',  # 事件等级是X
        r'(\d)级(?:事件|等级)',  # X级事件
    ]
    
    for pattern in level_patterns:
        match = re.search(pattern, cleaned_input)
        if match:
            event_level = match.group(1).strip()
            break
    
    # 提取页码和页大小
    page_match = re.search(r'第(\d+)页', cleaned_input)
    if page_match:
        page_no = page_match.group(1)
    
    size_match = re.search(r'每页(\d+)条', cleaned_input)
    if size_match:
        page_size = size_match.group(1)
    
    logger.info(f"提取事件查询参数结果：event_name={event_name}, event_status={event_status}, event_level={event_level}, page_no={page_no}, page_size={page_size}")
    return {
        "event_name": event_name,
        "event_status": event_status,
        "event_level": event_level,
        "page_no": page_no,
        "page_size": page_size
    }

# 函数调用代理
def function_calling_agent(user_input: str) -> Optional[str]:
    """
    使用LangChain函数调用代理处理用户的输入
    """
    logger.info(f"函数调用代理接收到查询: {user_input}")
    
    # 判断是否是普通问答，是否需要调用API的查询
    # 使用关键词组合判断是否需要API查询
    keyword_combinations = [
        # 1. 查询事件周边资源
        ["事", "公里"],
        ["事", "周边"],
        ["事", "附近"],
        ["事", "km"],
        # 2. 预览摄像头
        ["览", "摄像头"],
        ["览", "监控"],
        ["览", "camera"],
        ["查", "摄像头"],
        ["打", "摄像头"],
        ["观", "摄像头"],
        # 3. 一张图查看资源位置
        ["一张图", "查"],
        ["一张图", "定"],
        # 4. 终止事件预案
        ["终", "预案"],
        ["停", "预案"],
        # 5. 启动事件预案
        ["启", "预案"],
        ["开", "预案"],
        # # 6. 查询仓库信息
        # ["查", "仓库"],
        # ["搜", "仓库"],
        # ["大米", "仓库"],
        # ["查", "储备库"],
        # ["查", "物资储备库"],
        # 7. 查询事件信息
        ["查", "事件"],
        ["搜", "事件"],
        ["事", "列表"],
        # 8. 搜索应急预案
        ["查", "预案"],
        ["搜", "预案"]
    ]
    
    # 检查是否包含任何API关键词组合
    is_api_query = any(all(kw in user_input for kw in combination) for combination in keyword_combinations)
    
    # 额外检查特殊情况 - 匹配"XX仓库"模式
    # if not is_api_query and re.search(r'[\u4e00-\u9fa5]+仓库', user_input) is not None:
    #     logger.info("检测到XX仓库模式，设置为API查询")
    #     is_api_query = True
    
    # 输出调试信息
    logger.info(f"是否API查询: {is_api_query}")
    
    # 如果不是API查询，返回None，让主程序继续处理以实现流式输出
    if not is_api_query:
        logger.info("检测到普通问答请求，返回None以使用原有流程处理")
        return None
    
    # 1.检查是否是预案启动或终止请求
    is_plan_action_query = False
    detailed_api_result = None
    
    # 预案启动和终止关键词
    plan_start_keywords = ["启动预案", "开始预案", "执行预案", "启动应急预案", "开始应急预案"]
    plan_stop_keywords = ["终止预案", "停止预案", "结束预案", "取消预案", "终止应急预案", "停止应急预案"]
    
    if any(keyword in user_input for keyword in plan_start_keywords + plan_stop_keywords)or ("启动" in user_input and "预案" in user_input)or ("停止" in user_input and "预案" in user_input):
        is_plan_action_query = True
        
        # 确定是启动还是终止预案
        is_start_plan = any(keyword in user_input for keyword in plan_start_keywords)or ("启动" in user_input and "预案" in user_input)
        action_type = "启动" if is_start_plan else "终止"
        
        # 尝试提取事件名称
        event_name = ""
        # 首先尝试匹配"启动XX事件预案"这种格式
        event_matches_between = re.search(r'(启动|开始|执行|终止|停止|结束|取消)([\w\s]+?事件)(?:的)?(?:应急)?预案', user_input)
        if event_matches_between:
            event_name = event_matches_between.group(2).strip()
            logger.info(f"从'启动XX事件预案'格式中提取到事件名称: {event_name}")
        # 如果上面没匹配到，再尝试常规的"XX事件"模式，但排除包含"停止"、"终止"等词的匹配
        if not event_name:
            event_matches = re.findall(r'([\w\s]+?事件)', user_input)
            if event_matches:
                for match in event_matches:
                    # 排除包含"停止"、"终止"等词的匹配
                    if not any(word in match for word in ["停止", "终止", "结束", "取消"]):
                        event_name = match
                        break
        # 如果能直接提取出事件名称，直接调用函数
        if event_name:
            logger.info(f"直接提取到事件名称: {event_name} 用于{action_type}预案")
            try:
                # 根据操作类型调用相应的函数
                if is_start_plan:
                    result = event_start_plan(f"{event_name},")
                else:
                    result = event_stop_plan(f"{event_name},")
                logger.info(f"成功通过规则提取事件名称并调用{action_type}预案API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用{action_type}预案API失败: {str(e)}")
                detailed_api_result = f"{action_type}预案失败: {str(e)}"
    
    # 2.检查是否是一张图查看资源位置请求
    is_resource_position_query = False
    
    # 一张图查看资源位置关键词
    resource_position_keywords = ["一张图查看", "一张图定位", "查看位置", "定位资源", "查询位置", "资源位置"]
    
    if any(keyword in user_input for keyword in resource_position_keywords):
        is_resource_position_query = True
        
        # 提取资源名称
        resource_params = extract_resource_params(user_input)
        resource_name = resource_params["resource_name"]
        
        # 如果能直接提取出资源名称，直接调用函数
        if resource_name:
            logger.info(f"直接提取到资源名称: {resource_name}")
            try:
                # 直接调用one_map_position_resource函数
                result = one_map_position_resource(resource_name)
                logger.info("成功通过规则提取资源名称并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用资源位置查询API失败: {str(e)}")
                detailed_api_result = f"资源位置查询失败: {str(e)}"
    
    # 3.检查是否是摄像头预览请求
    is_camera_query = False
    
    # 摄像头预览关键词
    camera_keywords = ["预览摄像头", "预览监控", "查看摄像头", "查看监控", "打开摄像头", "打开监控", 
                      "开启摄像头", "开启监控", "监控点", "camera", "摄像头预览", "监控预览", 
                      "摄像机", "监控点位", "录像", "视频画面"]
    
    if any(keyword in user_input.lower() for keyword in camera_keywords):
        is_camera_query = True
        
        # 提取摄像头ID
        camera_params = extract_camera_params(user_input)
        camera_id = camera_params["camera_id"]
        
        # 如果能直接提取出摄像头ID，直接调用函数
        if camera_id:
            logger.info(f"直接提取到摄像头ID: {camera_id}")
            try:
                # 直接调用preview_camera函数
                result = preview_camera(camera_id)
                logger.info("成功通过规则提取摄像头ID并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用预览摄像头API失败: {str(e)}")
                detailed_api_result = f"预览摄像头失败: {str(e)}"
    
    # 4.检查是否是应急预案查询请求
    is_plan_query = False
    
    # 预案查询关键词
    plan_query_keywords = ["搜索应急预案", "搜索预案", "查询预案", "查找预案", "预案查询", "查询应急预案", 
                           "应急预案", "查找应急预案", "预案信息", "查看预案", "所有预案"]
    
    # 更灵活的关键词匹配
    if any(keyword in user_input for keyword in plan_query_keywords) or ("查询" in user_input and "预案" in user_input):
        is_plan_query = True
        
        # 提取预案类型
        plan_params = extract_plan_params(user_input)
        plan_type = plan_params["plan_type"]
        
        # 如果能提取出预案类型或者请求查询所有预案，直接调用函数
        if plan_type != None:
            logger.info(f"直接提取到预案类型: {plan_type}")
            try:
                # 直接调用search_emergency_plan函数
                result = search_emergency_plan(plan_type)
                logger.info("成功通过规则提取预案类型并调用API，跳过LLM代理调用")
                return result
            except Exception as e:
                logger.error(f"直接调用搜索应急预案API失败: {str(e)}")
                detailed_api_result = f"搜索应急预案失败: {str(e)}"
    
    # 5.检查是否是查询仓库信息请求
    is_repository_query = False
    
    # 仓库查询关键词
    # repository_keywords = ["查询仓库", "仓库信息", "仓库详情", "物资仓库", "应急物资", "储备库", "查找仓库", "找仓库"]
    
    # # 更灵活的关键词匹配
    # if any(keyword in user_input for keyword in repository_keywords) or ("查找" in user_input and "仓库" in user_input) or ("查询" in user_input and "仓库" in user_input) or ("大米仓库" in user_input) or (re.search(r'[\u4e00-\u9fa5]+仓库', user_input) is not None):
    #     is_repository_query = True
    #     logger.info(f"检测到仓库查询请求: {user_input}")
        
    #     # 提取仓库参数
    #     repository_params = extract_repository_params(user_input)
    #     repository_name = repository_params["repository_name"]
    #     page_num = repository_params["page_num"]
    #     page_size = repository_params["page_size"]
        
    #     # 如果能提取出仓库名称，直接调用函数
    #     if repository_name:
    #         logger.info(f"直接提取到仓库名称: {repository_name}")
    #         try:
    #             # 直接调用query_repository函数
    #             query_param = f"{repository_name},{page_num},{page_size}"
    #             result = query_repository(query_param)
    #             logger.info("成功通过规则提取仓库参数并调用API，跳过LLM代理调用")
    #             return result
    #         except Exception as e:
    #             logger.error(f"直接调用查询仓库信息API失败: {str(e)}")
    #             detailed_api_result = f"查询仓库信息失败: {str(e)}"
    
    # 6.检查是否是查询事件信息请求
    is_event_info_query = False
    
    # 事件查询关键词
    event_info_keywords = ["查询事件", "搜索事件", "事件信息", "事件列表", "事件查询", "所有事件", "全部事件"]
    
    if any(keyword in user_input for keyword in event_info_keywords):
        is_event_info_query = True
        
        # 提取事件查询参数
        event_params = extract_event_query_params(user_input)
        
        # 构建查询参数字符串
        query_param = f"{event_params['page_no']},{event_params['page_size']},{event_params['event_status']},{event_params['event_name']},{event_params['event_level']}"
        
        logger.info(f"事件查询参数: {query_param}")
        try:
            # 直接调用query_all_event_infos函数
            result = query_all_event_infos(query_param)
            logger.info("成功通过规则提取事件查询参数并调用API，跳过LLM代理调用")
            return result
        except Exception as e:
            logger.error(f"直接调用查询所有事件信息API失败: {str(e)}")
            detailed_api_result = f"查询事件信息失败: {str(e)}"
    
    # 7.检查是否是事件周边资源查询请求
    is_resource_query = False
    
    # 尝试直接提取事件名称和距离，如果是简单格式的话
    resource_keywords = ["公里范围内的应急资源", "公里内的应急资源", "公里范围的应急资源", "公里的应急资源", "公里应急资源"]
    if any(keyword in user_input for keyword in resource_keywords)or ("公里" in user_input and "应急资源" in user_input):
        is_resource_query = True

        # 提取事件名称和距离
        params = extract_event_params(user_input)
        event_name = params["event_name"]
        distance = params["distance"]
            
        # 如果能直接提取出事件名称和距离，直接调用查询函数
        if event_name and distance:
            logger.info(f"直接提取到参数：事件名称={event_name}, 距离={distance}公里")
            try:
                # 直接调用API获取详细结果
                api_config = load_api_config()
                if not api_config:
                    return "API配置加载失败"
                
                api_url = get_api_url("default", "queryEventSurroundResource")
                if not api_url:
                    return "API URL构建失败"
                    
                headers = {
                    "Content-Type": "application/json",
                    "Token": api_config['token']
                }
                
                payload = {
                    "eventName": event_name,
                    "arroundDistance": distance,
                    "resourceType": ""
                }
                
                response = requests.post(api_url, json=payload, headers=headers, verify=False)
                response_data = response.json()
                
                if response_data.get("code") == "0":
                    resources = response_data.get("data", [])
                    
                    if not resources:
                        detailed_api_result = f"{event_name}{distance}公里范围内未找到应急资源"
                    else:
                        # 按类型分组
                        resource_groups = {}
                        for resource in resources:
                            res_type = resource.get("type", "other")
                            if res_type not in resource_groups:
                                resource_groups[res_type] = []
                            resource_groups[res_type].append(resource)
                        
                        # 类型映射表（英文到中文）
                        type_mapping = {
                            "rescue_team": "救援队伍",
                            "repository": "应急仓库", 
                            "refuge": "避难场所",
                            "medical_health": "医疗卫生",
                            "hazard": "危险隐患",
                            "emergency_expert": "医疗专家",
                            "protection_target": "防护目标",
                            "material": "物资",
                            "equipment": "装备",
                            "camera": "监控点",
                            "common": "监控点",
                            "person": "人员",
                            "enterpriseInfo": "企业信息",
                            "other": "其他资源"
                        }
                        
                        # 格式化输出
                        result = [f"{event_name}{distance}公里范围内的应急资源如下："]
                        
                        # 按类型处理资源
                        for res_type in ["rescue_team", "repository", "medical_health", "emergency_expert", 
                                        "material", "equipment", "protection_target", "enterpriseInfo", 
                                        "common", "other"]:
                            if res_type in resource_groups and resource_groups[res_type]:
                                type_name = type_mapping.get(res_type, res_type)
                                result.append(f"\n**{type_name}:**")
                                
                                for item in resource_groups[res_type]:
                                    name = item.get("name", "未知")
                                    item_distance = item.get("distance", "未知")
                                    phone = item.get("phoneNumber", "")
                                    
                                    resource_info = f"- {name}"
                                    if item_distance:
                                        resource_info += f"，距离{item_distance}公里"
                                    if phone:
                                        resource_info += f"，联系电话{phone}"
                                    
                                    result.append(resource_info)
                        
                        detailed_api_result = "\n".join(result)
                        
                        # 如果成功提取参数并得到结果，直接返回，不再调用LLM代理
                        logger.info("成功通过规则提取参数并调用API，跳过LLM代理调用")
                        return detailed_api_result
                else:
                    error_msg = response_data.get("msg", "未知错误")
                    detailed_api_result = f"{error_msg}"
                    
            except Exception as e:
                logger.error(f"直接调用API函数失败: {str(e)}")
                detailed_api_result = f"API调用失败: {str(e)}"
    
    # 8.只有在直接参数提取失败的情况下才创建LLM代理
    if not detailed_api_result:
        logger.info("参数提取不完整或处理复杂查询，创建LLM代理")
        
        # 创建代理
        agent = create_agent()
        if not agent:
            logger.error("创建代理失败")
            return "系统错误：无法创建智能代理，请联系管理员"
        
        try:
            # 使用代理处理查询
            response = agent.invoke({"input": user_input})
            logger.info("代理处理完成")
            
            # 获取最终输出
            if isinstance(response, dict) and "output" in response:
                agent_output = filter_think_tags(response["output"])  # 使用过滤器处理输出
                
                # 快速检测是否包含"未找到符合条件"类型的响应
                if "未找到符合条件的" in agent_output or "未找到" in agent_output and "事件" in agent_output:
                    logger.info("检测到空结果返回，直接返回")
                    return agent_output
            else:
                agent_output = filter_think_tags(str(response))  # 使用过滤器处理输出
            
            # 格式化结果
            formatted_response = format_agent_response(agent_output)
            return formatted_response
            
        except Exception as e:
            logger.error(f"代理处理错误: {str(e)}")
            return f"处理查询时出错: {str(e)}"
    
    # 如果有API结果但LLM处理失败，返回API结果
    return detailed_api_result

# 测试函数
if __name__ == "__main__":
    # 测试查询
    test_input = "西兴街道火灾事件5公里范围内的应急资源"
    print(f"测试查询: {test_input}")
    
    # 执行查询
    result = function_calling_agent(test_input)
    print(result)