# RAG知识库问答系统 Docker 部署方案

本文档详细说明如何使用Docker容器化部署RAG知识库问答系统。

## 1. 系统架构

在Docker环境中，我们将把系统拆分为以下几个容器：

1. **Web应用容器**：运行FastAPI应用，处理用户请求和文档上传
2. **Qdrant容器**：运行Qdrant向量数据库，存储文档向量
3. **Ollama容器**：运行Ollama服务，提供大语言模型和嵌入模型

这种微服务架构使系统更加灵活，便于扩展和维护。

## 2. 目录结构准备

在打包前，建议按以下结构组织项目文件：

```
RAG_demo6/
├── app/                    # 应用主目录
│   ├── main.py             # 主应用入口
│   ├── privateGPT_res.py   # RAG检索和生成逻辑
│   ├── constants.py        # 常量定义
│   ├── Private_GPT/        # 核心功能模块
│   │   └── ingest_600.py   # 文档摄取和处理
│   └── static/             # 静态资源
├── config/                 # 配置文件目录
│   └── config.ini          # 配置文件
├── data/                   # 数据目录
│   ├── db/                 # 向量数据库存储目录
│   └── documents/          # 源文档存储目录
├── docker/                 # Docker相关文件
│   ├── Dockerfile          # Web应用Dockerfile
│   ├── docker-compose.yml  # 容器编排文件
│   └── .dockerignore       # Docker忽略文件
└── requirements.txt        # 依赖列表
```

## 3. Dockerfile

创建`docker/Dockerfile`文件，用于构建Web应用容器：

```dockerfile
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libreoffice \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-eng \
    poppler-utils \
    antiword \
    libmagic1 \
    pandoc \
    git \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ /app/
COPY config/ /app/config/

# 创建必要的目录
RUN mkdir -p /app/data/db /app/data/documents /app/log

# 设置环境变量
ENV PYTHONPATH=/app
ENV LIBREOFFICE_PATH=/usr/bin/soffice
ENV TESSERACT_PATH=/usr/bin

# 暴露端口
EXPOSE 8089

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8089"]
```

## 4. Docker Compose 配置

创建`docker/docker-compose.yml`文件，用于编排多个容器：

```yaml
version: '3.8'

services:
  web:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8089:8089"
    volumes:
      - ../data:/app/data
      - ../log:/app/log
    environment:
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - qdrant
      - ollama
    restart: unless-stopped

  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  qdrant_data:
  ollama_data:
```

## 5. 配置文件调整

需要修改`config.ini`文件以适应Docker环境：

```ini
[PATHS]
LIBREOFFICE_PATH = /usr/bin/soffice
TESSERACT_PATH = /usr/bin

[DATABASE]
PERSIST_DIRECTORY = /app/data/db
VECTOR_DB_TYPE = qdrant

[DOCUMENTS]
SOURCE_DIRECTORY = /app/data/documents

[EMBEDDINGS]
MODEL_NAME = /app/Private_GPT/sentence-transformers/bge-large-zh-v1.5
EMBEDDING_TYPE = ollama
BASE_URL = http://ollama:11434
OLLAMA_MODEL = bge-large:latest

[CHUNKING]
CHUNK_SIZE = 1000
CHUNK_OVERLAP = 100

[LLM]
MODEL = deepseek-r1:32b
TARGET_SOURCE_CHUNKS = 5

[OCR]
LANGUAGES = chi_sim+eng

[QDRANT]
HOST = qdrant
PORT = 6333
COLLECTION_NAME_STORE = documents
COLLECTION_NAME_QUERY = documents
```

## 6. 打包脚本

创建一个打包脚本`docker/build.sh`，用于准备Docker构建环境：

```bash
#!/bin/bash

# 创建临时构建目录
mkdir -p build/{app,config,data,docker}

# 复制应用代码
cp -r ../main.py ../privateGPT_res.py ../Private_GPT ../static build/app/

# 复制配置文件
cp ../config.ini build/config/

# 复制Docker文件
cp Dockerfile docker-compose.yml build/docker/

# 复制依赖文件
cp ../requirements.txt build/

# 创建.dockerignore文件
cat > build/.dockerignore << EOF
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
**/.Python
**/.env
**/.venv
**/env/
**/venv/
**/ENV/
**/env.bak/
**/venv.bak/
EOF

echo "构建目录已准备完成，位于 build/ 目录"
```

## 7. 部署步骤

### 7.1 准备工作

1. 确保服务器已安装Docker和Docker Compose
2. 如果需要GPU支持，确保安装了NVIDIA Container Toolkit

### 7.2 模型准备

在部署前，需要确保Ollama服务器上有所需的模型：

```bash
# 拉取嵌入模型
ollama pull bge-large:latest

# 拉取大语言模型
ollama pull deepseek-r1:32b
```

### 7.3 部署流程

1. 将项目文件传输到服务器
2. 进入项目目录，执行构建和启动命令：

```bash
cd docker
./build.sh
cd build
docker-compose -f docker/docker-compose.yml up -d
```

3. 检查容器状态：

```bash
docker-compose -f docker/docker-compose.yml ps
```

4. 查看日志：

```bash
docker-compose -f docker/docker-compose.yml logs -f web
```

### 7.4 访问应用

部署完成后，可以通过以下URL访问应用：

```
http://服务器IP:8089
```

## 8. 数据持久化

Docker Compose配置中已设置了数据卷，确保以下数据可以持久化：

- Qdrant数据库存储在`qdrant_data`卷中
- Ollama模型存储在`ollama_data`卷中
- 上传的文档和应用日志通过挂载主机目录实现持久化

## 9. 扩展与优化

### 9.1 使用Nginx作为反向代理

对于生产环境，建议添加Nginx作为反向代理：

```yaml
  nginx:
    image: nginx:latest
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
```

### 9.2 资源限制

可以为各个容器设置资源限制，避免单个服务占用过多资源：

```yaml
  web:
    # ...其他配置...
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
```

### 9.3 健康检查

添加健康检查，确保服务正常运行：

```yaml
  web:
    # ...其他配置...
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 10. 故障排除

### 10.1 容器启动失败

检查日志：
```bash
docker-compose logs web
```

### 10.2 模型加载问题

确保Ollama容器有足够的资源加载模型：
```bash
docker stats ollama
```

### 10.3 文件权限问题

确保挂载的卷有正确的权限：
```bash
docker-compose exec web ls -la /app/data
```

## 11. 安全考虑

1. 不要在Dockerfile中包含敏感信息
2. 考虑为Qdrant和Ollama服务添加访问控制
3. 使用非root用户运行容器
4. 定期更新基础镜像和依赖

## 12. 备份策略

定期备份重要数据：

```bash
# 备份Qdrant数据
docker run --rm -v qdrant_data:/data -v $(pwd)/backups:/backups \
  ubuntu tar -czf /backups/qdrant_backup_$(date +%Y%m%d).tar.gz /data

# 备份上传的文档
docker run --rm -v $(pwd)/data:/data -v $(pwd)/backups:/backups \
  ubuntu tar -czf /backups/documents_backup_$(date +%Y%m%d).tar.gz /data/documents
```

## 结论

通过Docker容器化部署，RAG知识库问答系统可以实现更好的可移植性、可扩展性和隔离性。这种部署方式适合各种规模的应用场景，从开发测试到生产环境都可以平滑过渡。 