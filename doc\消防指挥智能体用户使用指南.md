# 消防指挥智能体使用指南

## 1. 系统简介

消防指挥智能体是一个基于人工智能技术的知识库问答系统，能够帮助用户快速获取消防指挥相关知识和信息。系统支持智能问答、智能体功能、文档上传、文档提问、会话历史管理、用户反馈、已上传文档管理等功能，为消防指挥工作提供智能化支持。

## 2. 系统主要功能

### 2.1 智能问答

您可以直接在主界面输入问题进行咨询，智能体会根据知识库内容给出专业回答。

**使用方法**：

1. 在主界面底部的输入框中输入您的问题
2. 点击发送按钮或按回车键提交问题
3. 系统会快速分析您的问题并给出回答

**示例问题**：

- 高层建筑火灾扑救的关键点是什么？
- 消防指挥中心的主要职责有哪些？
- 如何处理化学品泄漏引发的火灾？

### 2.2 文档上传功能

您可以上传自己的文档到系统中，上传后的文档将被智能体分析并纳入知识库，使智能体能够回答与您上传文档相关的问题。

**支持的文档格式**：

- Word文档（.docx）- 主要支持格式
- PDF文档（.pdf）
- 文本文件（.txt）
- CSV文件（.csv）
- Markdown文件（.md）

**上传步骤**：

1. 点击主界面右上角的"添加知识"按钮
2. 在弹出的对话框中输入知识标题
3. 选择或输入所属部门
4. 将文件拖放到上传区域，或点击上传区域选择文件
5. 点击"提交"按钮开始上传
6. 等待系统处理文档（处理时间取决于文档大小和复杂度）
7. 处理完成后，系统会显示上传成功提示

**注意事项**：

- 上传的Word文档（.docx）会被更好处理，系统对此格式支持最完善
- 文档处理过程中请勿关闭页面
- 文档处理完成后，您可以立即提问与该文档相关的问题
- 您可以点击"查看已上传文档"按钮浏览所有已上传的文档

### 2.3 智能体功能

系统集成了专门用于应急管理和资源调度的智能体功能，可以帮助您快速获取事件信息、查询周边资源、管理预案等。

**使用方法**：

1. 在主界面底部的输入框中输入与应急管理相关的指令
2. 点击发送按钮或按回车键提交指令
3. 系统会自动识别您的意图并调用相应的功能

**主要功能**：

- **事件周边资源查询**：查询特定事件周边的应急资源，如救援队伍、物资仓库等
- **预案管理**：启动或终止特定事件的应急预案
- **资源定位**：在一张图上查看并定位特定资源的位置
- **事件查询**：查询系统中的所有事件信息
- **预案查询**：搜索特定类型的应急预案
- **摄像头预览**：预览特定位置的监控摄像头

**示例指令**：

- 杭州暴雨事件5公里范围内的应急资源？
- 帮我查找下杭州暴雨事件详情
- 杭州暴雨事件启动预案
- 查询森林火灾预案
- 一张图查看防汛抗旱物资储备库
- 预览摄像头138-Camera 01

### 2.4 文档提问功能

文档提问功能允许您针对特定文档进行分析和提问，无需将文档添加到知识库中。

**使用步骤**：

1. 点击主界面右上角的"文档提问"按钮
2. 系统会跳转到文档分析页面
3. 点击"上传文档"按钮选择要分析的文档（最多5个）
4. 选择文档后，系统会显示已上传文件的列表
5. 在底部输入框中输入与文档相关的问题
6. 点击发送按钮或按回车键提交问题
7. 系统会直接分析上传的文档并给出回答

**示例问题**：

- 这些文档中提到的应急处置方案包含哪些关键步骤？
- 根据这些文档，西宁市应急指挥部成员单位职责是什么？

**注意事项**：

- 文档提问功能仅分析您上传的文档，不会将这些文档永久存储到知识库中
- 每次会话最多可上传 5 个文档进行分析
- 支持的文件格式与知识上传功能相同
- 如需返回主页面，点击左上角的"返回主页"按钮

### 2.5 会话历史管理

系统会自动保存您的对话历史，方便您随时查看和继续之前的对话。

**主要功能**：

- **新建会话**：点击左侧边栏的"新建会话"按钮可以开始一个新的对话
- **查看历史会话**：在左侧边栏的"历史记录"区域可以看到您的所有历史会话
- **继续历史会话**：点击任一历史会话可以继续该对话
- **分页浏览**：当历史记录较多时，可以使用分页导航浏览更多历史会话

**使用技巧**：

- 定期使用"新建会话"功能开始新的对话，可以避免上下文混淆
- 历史记录可以通过点击标题栏的折叠/展开按钮进行折叠或展开

### 2.6 用户反馈功能

系统提供了用户反馈功能，您可以对智能体的回答进行评价和提供建议，帮助我们不断改进系统。

**使用方法**：

1. 在每个智能体回答的右下角，您可以看到"评价"按钮
2. 点击"评价"按钮，将显示评价选项（好评/差评）
3. 选择评价类型后，您可以选择添加评论来提供更详细的反馈
4. 点击"提交"按钮完成反馈

**注意事项**：

- 您的反馈将被存储并用于改进系统
- 评价是可选的，但您的反馈对我们非常宝贵

### 2.7 已上传文档管理功能

系统提供了已上传文档管理功能，您可以浏览和管理已上传到知识库的所有文档。

**使用方法**：

1. 在添加知识对话框中，点击"查看已上传文档"按钮
2. 系统会显示所有已上传的文档，按文件夹组织
3. 您可以根据部门进行筛选，快速找到目标文档

**主要功能**：

- **文档浏览**：查看所有已上传的文档及其元数据（标题、部门、上传时间等）
- **部门筛选**：根据部门筛选文档，快速找到目标文档
- **刷新列表**：点击"刷新列表"按钮可以获取最新的文档列表

## 3. 使用技巧

1. **提问要具体**：提供具体、明确的问题能够获得更准确的回答
2. **上传专业文档**：上传与您工作领域相关的专业文档，可以让智能体更好地理解您的专业术语和需求
3. **利用文档提问**：当需要快速分析特定文档而不想将其添加到知识库时，使用文档提问功能
4. **查看思考过程**：系统回答问题时会显示"思考过程"，展开它可以了解系统如何分析您的问题
5. **定期清理会话**：使用"新建会话"功能开始新的对话，可以避免上下文混淆
6. **提供反馈**：对智能体的回答进行评价和提供建议，帮助我们改进系统

## 4. 常见问题

**Q: 为什么我上传的文档没有被正确处理？**
A: 请确保您上传的文档格式正确，特别是对于Word文档，建议使用.docx格式。如果问题持续，请联系系统管理员。

**Q: 为什么智能体没有回答我的问题？**
A: 可能是因为知识库中没有相关信息，或者您的问题表述不够清晰。尝试重新表述问题，或上传包含相关信息的文档。

**Q: 如何获取最佳回答效果？**
A: 提供清晰、具体的问题，并确保相关知识已经通过文档上传到系统中。对于特定文档的分析，使用文档提问功能并上传相关文档。
