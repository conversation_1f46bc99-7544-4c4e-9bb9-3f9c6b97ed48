// 管理员反馈页面脚本
document.addEventListener('DOMContentLoaded', function() {
    // 状态变量
    let currentPage = 1;
    let pageSize = 20;
    let totalItems = 0;
    let totalPages = 1;
    let currentFilters = {
        rating: null,
        includeDeleted: false
    };

    // DOM 元素
    const feedbackList = document.getElementById('feedback-list');
    const ratingFilter = document.getElementById('rating-filter');
    const deletedFilter = document.getElementById('deleted-filter');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    const pageSizeSelect = document.getElementById('page-size');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const currentPageSpan = document.getElementById('current-page');
    const totalPagesSpan = document.getElementById('total-pages');
    const totalItemsSpan = document.getElementById('total-items');
    const showingFromSpan = document.getElementById('showing-from');
    const showingToSpan = document.getElementById('showing-to');
    const totalCountSpan = document.getElementById('total-count');
    
    // 模态框元素
    const modal = document.getElementById('feedback-modal');
    const closeModal = document.querySelector('.close');
    const modalUserMessage = document.getElementById('modal-user-message');
    const modalAssistantResponse = document.getElementById('modal-assistant-response');
    const modalThinkingProcess = document.getElementById('modal-thinking-process');
    const modalSourceDocuments = document.getElementById('modal-source-documents');
    const modalRating = document.getElementById('modal-rating');
    const modalCreatedAt = document.getElementById('modal-created-at');
    const modalComment = document.getElementById('modal-comment');
    const modalSessionId = document.getElementById('modal-session-id');
    const modalMessageId = document.getElementById('modal-message-id');
    const modalStatus = document.getElementById('modal-status');

    // 初始化
    init();

    // 初始化函数
    function init() {
        // 设置初始页面大小
        pageSize = parseInt(pageSizeSelect.value);
        
        // 加载反馈数据
        loadFeedbackData();
        
        // 添加事件监听器
        applyFiltersBtn.addEventListener('click', applyFilters);
        resetFiltersBtn.addEventListener('click', resetFilters);
        pageSizeSelect.addEventListener('change', changePageSize);
        prevPageBtn.addEventListener('click', goToPrevPage);
        nextPageBtn.addEventListener('click', goToNextPage);
        closeModal.addEventListener('click', hideModal);
        
        // 点击模态框外部关闭模态框
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                hideModal();
            }
        });
    }

    // 加载反馈数据
    function loadFeedbackData() {
        showLoading();
        
        // 构建API URL
        const url = buildApiUrl();
        
        // 发起API请求
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                displayFeedbackData(data);
                updatePagination(data);
            })
            .catch(error => {
                showError('加载数据失败: ' + error.message);
            })
            .finally(() => {
                hideLoading();
            });
    }

    // 构建API URL
    function buildApiUrl() {
        const params = new URLSearchParams();
        params.append('limit', pageSize);
        params.append('offset', (currentPage - 1) * pageSize);
        
        if (currentFilters.rating !== null) {
            params.append('rating', currentFilters.rating);
        }
        
        params.append('include_deleted', currentFilters.includeDeleted);
        
        return `/admin/permanent_feedback/?${params.toString()}`;
    }

    // 显示反馈数据
    function displayFeedbackData(data) {
        // 清空列表
        feedbackList.innerHTML = '';
        
        // 检查是否有数据
        if (!data.items || data.items.length === 0) {
            feedbackList.innerHTML = '<div class="no-data">没有找到符合条件的反馈数据</div>';
            return;
        }
        
        // 遍历数据并创建列表项
        data.items.forEach(item => {
            const feedbackItem = createFeedbackItem(item);
            feedbackList.appendChild(feedbackItem);
        });
    }

    // 创建反馈列表项
    function createFeedbackItem(item) {
        const div = document.createElement('div');
        div.className = 'feedback-item' + (item.is_deleted ? ' deleted-item' : '');
        
        // 格式化时间
        const createdAt = new Date(item.created_at).toLocaleString();
        
        // 截断问题和回答文本
        const questionText = truncateText(item.user_message, 100);
        const answerText = truncateText(item.assistant_response, 150);
        
        // 设置评分标签
        const ratingLabel = item.rating === 1 ? '正面评价' : '负面评价';
        const ratingClass = item.rating === 1 ? 'rating-positive' : 'rating-negative';
        
        div.innerHTML = `
            <div class="feedback-header">
                <div class="feedback-meta">
                    <div class="feedback-id">ID: ${item.id}</div>
                    <div class="feedback-timestamp">${createdAt}</div>
                </div>
                <div class="feedback-rating">
                    <span class="rating-badge ${ratingClass}">${ratingLabel}</span>
                    ${item.is_deleted ? '<span class="deleted-badge">已删除</span>' : ''}
                </div>
            </div>
            <div class="feedback-content">
                <div class="feedback-question">${questionText}</div>
                <div class="feedback-answer">${answerText}</div>
                ${item.comment ? `<div class="feedback-comment">"${item.comment}"</div>` : ''}
            </div>
            <div class="feedback-actions">
                <button class="btn btn-primary view-details" data-id="${item.id}">查看详情</button>
                ${!item.is_deleted ? `<button class="btn btn-danger delete-feedback" data-id="${item.id}">删除</button>` : ''}
            </div>
        `;
        
        // 添加事件监听器
        div.querySelector('.view-details').addEventListener('click', () => showFeedbackDetails(item));
        
        const deleteBtn = div.querySelector('.delete-feedback');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => deleteFeedback(item.id));
        }
        
        return div;
    }

    // 显示反馈详情
    function showFeedbackDetails(item) {
        // 填充模态框内容
        modalUserMessage.textContent = item.user_message || '无内容';
        modalAssistantResponse.textContent = item.assistant_response || '无内容';
        modalThinkingProcess.textContent = item.thinking_process || '无内容';
        modalSourceDocuments.textContent = item.source_documents || '无引用文档';
        
        // 设置评分标签
        const ratingLabel = item.rating === 1 ? '正面评价' : '负面评价';
        const ratingClass = item.rating === 1 ? 'rating-positive' : 'rating-negative';
        modalRating.className = 'rating-badge ' + ratingClass;
        modalRating.textContent = ratingLabel;
        
        // 设置时间
        modalCreatedAt.textContent = new Date(item.created_at).toLocaleString();
        
        // 设置评论
        modalComment.textContent = item.comment || '无评论';
        
        // 设置会话信息
        modalSessionId.textContent = item.session_id || '未知';
        modalMessageId.textContent = item.message_id || '未知';
        modalStatus.textContent = item.is_deleted ? '已删除' : '正常';
        
        // 显示模态框
        modal.style.display = 'block';
    }

    // 隐藏模态框
    function hideModal() {
        modal.style.display = 'none';
    }

    // 删除反馈
    function deleteFeedback(id) {
        if (!confirm('确定要删除这条反馈吗？此操作不可恢复。')) {
            return;
        }
        
        fetch(`/admin/permanent_feedback/${id}`, {
            method: 'DELETE'
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('删除失败');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    loadFeedbackData(); // 重新加载数据
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('删除失败: ' + error.message);
            });
    }

    // 应用筛选
    function applyFilters() {
        currentFilters.rating = ratingFilter.value ? parseInt(ratingFilter.value) : null;
        currentFilters.includeDeleted = deletedFilter.checked;
        currentPage = 1; // 重置到第一页
        loadFeedbackData();
    }

    // 重置筛选
    function resetFilters() {
        ratingFilter.value = '';
        deletedFilter.checked = false;
        currentFilters.rating = null;
        currentFilters.includeDeleted = false;
        currentPage = 1; // 重置到第一页
        loadFeedbackData();
    }

    // 更改每页显示数量
    function changePageSize() {
        pageSize = parseInt(pageSizeSelect.value);
        currentPage = 1; // 重置到第一页
        loadFeedbackData();
    }

    // 上一页
    function goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            loadFeedbackData();
        }
    }

    // 下一页
    function goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            loadFeedbackData();
        }
    }

    // 更新分页信息
    function updatePagination(data) {
        totalItems = data.total || 0;
        totalPages = Math.ceil(totalItems / pageSize) || 1;
        
        // 更新显示
        currentPageSpan.textContent = currentPage;
        totalPagesSpan.textContent = totalPages;
        totalItemsSpan.textContent = totalItems;
        totalCountSpan.textContent = `(${totalItems})`;
        
        // 计算显示范围
        const from = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;
        const to = Math.min(currentPage * pageSize, totalItems);
        showingFromSpan.textContent = from;
        showingToSpan.textContent = to;
        
        // 更新按钮状态
        prevPageBtn.disabled = currentPage <= 1;
        nextPageBtn.disabled = currentPage >= totalPages;
    }

    // 显示加载中
    function showLoading() {
        feedbackList.innerHTML = `
            <div class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i> 加载中...
            </div>
        `;
    }

    // 隐藏加载中
    function hideLoading() {
        const loadingIndicator = feedbackList.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    // 显示错误
    function showError(message) {
        feedbackList.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i> ${message}
            </div>
        `;
    }

    // 截断文本
    function truncateText(text, maxLength) {
        if (!text) return '无内容';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
});
