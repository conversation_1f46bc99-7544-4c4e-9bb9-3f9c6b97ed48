[PATHS]
# LibreOffice 可执行文件路径
# Windows 示例: C:\Program Files\LibreOffice\program\soffice.exe
# Linux 示例: /usr/bin/soffice
LIBREOFFICE_PATH = C:\Program Files\LibreOffice\program\soffice.exe
# Tesseract OCR 可执行文件路径
# Windows 示例: D:\Program Files\Tesseract-OCR
# Linux 示例: /usr/bin
TESSERACT_PATH = D:\Program Files\Tesseract-OCR

[DATABASE]
# 向量数据库存储目录
PERSIST_DIRECTORY = Private_GPT/db_DOC_basic_600
VECTOR_DB_TYPE = qdrant
SCORE_THRESHOLD = 0.5

[QDRANT]
HOST = localhost
PORT = 7541
# 存储集合名称，如果为空则使用文件夹名称 示例：documents、water_test、spyj
COLLECTION_NAME_STORE = documents
# 查询集合名称，如果为空则使用最新的集合 示例：documents、water_test、spyj
COLLECTION_NAME_QUERY = documents

[POSTGRES]
# PostgreSQL数据库连接信息
HOST = localhost
PORT = 7543
DATABASE = postgres
USER = postgres
PASSWORD = hik12345+

[DOCUMENTS]
# 源文档目录
SOURCE_DIRECTORY = Private_GPT\source_documents

[EMBEDDINGS]
# 嵌入模型路径
MODEL_NAME = Private_GPT\sentence-transformers\bge-large-zh-v1.5
# 嵌入模型类型: huggingface, ollama
EMBEDDING_TYPE = huggingface
# Ollama服务的基础URL，当EMBEDDING_TYPE=ollama时使用
BASE_URL = http://localhost:11434
# Ollama模型名称，当EMBEDDING_TYPE=ollama时使用
OLLAMA_MODEL = bge-large:latest

[CHUNKING]
# 文本分块大小
CHUNK_SIZE = 1000
# 文本分块重叠大小
CHUNK_OVERLAP = 100

[LLM]
# 大语言模型类型：ollama或openai
LLM_TYPE = ollama
# 大语言模型名称：
# - 当LLM_TYPE=ollama时，如：deepseek-r1:32b、qwq、DeepSeek-R1-Distill-Qwen-32B、gemma3:27b、qwen3:30b等
# - 当LLM_TYPE=openai时，如：deepseek-chat、gpt-3.5-turbo、gpt-4等
MODEL = gemma3:27b
# Ollama基础URL，当LLM_TYPE=ollama时使用 url示例： http://************:11434、http://localhost:11434
OLLAMA_BASE_URL = http://localhost:11434
# OpenAI API密钥，当LLM_TYPE=openai时使用 示例：sk-eefa01dd15c149e1a7245c2089294f9a、sk-123456
OPENAI_API_KEY = sk-eefa01dd15c149e1a7245c2089294f9a
# OpenAI API基础URL，当LLM_TYPE=openai时使用，默认为官方API 示例：https://api.deepseek.com、http://*************:8000/v1/
OPENAI_API_BASE = https://api.deepseek.com
#OPENAI_API_BASE = https://maas.hikvision.com.cn/v1
# 检索时返回的文档块数量
TARGET_SOURCE_CHUNKS = 5

[OCR]
# OCR 语言设置
# 中文简体+英文: chi_sim+eng
# 中文繁体+英文: chi_tra+eng
# 仅英文: eng
LANGUAGES = chi_sim+eng



[API]
# API基础URL（服务器地址和端口）*************、*************
BASE_URL = https://*************
# API路径配置 - 默认路径
DEFAULT_PATH = /emdispatch-web/third/v1/eventApi/app/
# API路径配置 - 仓库接口路径
REPOSITORY_PATH = /ermanage-web/third/v1/resourceApi/resourcePage/
# API路径配置 - 预案接口路径
PLAN_PATH = /emplan-web/third/v1/resourceApi/plan/
# 内容类型
CONTENT_TYPE = application/json
# 访问令牌
TOKEN = SElLIEV2WU5lZG9RRStvVUtTaTQ6Y2pvMEw5cVN5YzNMUi8zRFdrR3JtUVZ1a0NNczluWXhFemNhb3dXR01vUT06MTc0NDc4NjE4MTc3Mg==
