server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://web:8089;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态文件缓存
    location /static/ {
        proxy_pass http://web:8089/static/;
        proxy_set_header Host $host;
        proxy_cache_valid 200 1d;
        add_header Cache-Control "public, max-age=86400";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://web:8089/health;
        proxy_set_header Host $host;
        access_log off;
        proxy_cache_bypass $http_pragma;
        proxy_cache_revalidate on;
        expires off;
        add_header Cache-Control no-cache;
    }
} 