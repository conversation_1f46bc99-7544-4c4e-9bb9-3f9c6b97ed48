#!/bin/bash

# 创建临时构建目录
mkdir -p build/{app,config,data,docker}

# 复制应用代码
cp -r ../main.py ../privateGPT_res.py ../Private_GPT ../static build/app/

# 复制配置文件
cp ../config.ini build/config/

# 复制Docker文件
cp Dockerfile docker-compose.yml build/docker/

# 复制依赖文件
cp ../requirements.txt build/

# 创建.dockerignore文件
cat > build/.dockerignore << EOF
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
**/.Python
**/.env
**/.venv
**/env/
**/venv/
**/ENV/
**/env.bak/
**/venv.bak/
EOF

echo "构建目录已准备完成，位于 build/ 目录" 