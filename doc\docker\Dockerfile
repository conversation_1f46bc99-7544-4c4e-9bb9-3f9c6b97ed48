FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libreoffice \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-eng \
    poppler-utils \
    antiword \
    libmagic1 \
    pandoc \
    git \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件并安装
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ /app/
COPY config/ /app/config/

# 创建必要的目录
RUN mkdir -p /app/data/db /app/data/documents /app/log

# 设置环境变量
ENV PYTHONPATH=/app
ENV LIBREOFFICE_PATH=/usr/bin/soffice
ENV TESSERACT_PATH=/usr/bin

# 暴露端口
EXPOSE 8089

# 启动命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8089"] 