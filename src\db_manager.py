import psycopg2
import psycopg2.extras
import uuid
import os
import configparser
from datetime import datetime
import logging

# 设置日志
logger = logging.getLogger("db_manager")

# 从配置文件加载数据库连接参数
def load_db_config():
    try:
        config = configparser.ConfigParser()
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
        config.read(config_path, encoding='utf-8')

        # 从配置文件读取PostgreSQL连接信息
        db_config = {
            "host": config.get("POSTGRES", "HOST", fallback="***********"),
            "port": config.getint("POSTGRES", "PORT", fallback=7543),
            "database": config.get("POSTGRES", "DATABASE", fallback="postgres"),
            "user": config.get("POSTGRES", "USER", fallback="postgres"),
            "password": config.get("POSTGRES", "PASSWORD", fallback="hik12345+")
        }

        logger.info(f"从配置文件加载数据库连接信息成功: {db_config['host']}:{db_config['port']}")
        return db_config
    except Exception as e:
        logger.error(f"从配置文件加载数据库连接信息失败: {str(e)}")
        # 返回默认配置
        return {
            "host": "***********",
            "port": 7543,
            "database": "postgres",
            "user": "postgres",
            "password": "hik12345+"
        }

# 加载数据库配置
DB_CONFIG = load_db_config()

class DBManager:
    def __init__(self, db_config=DB_CONFIG):
        """初始化数据库管理器"""
        self.db_config = db_config
        self.init_db()

    def get_connection(self):
        """获取数据库连接"""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise

    def init_db(self):
        """初始化数据库，创建必要的表"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 先删除可能存在的外键约束
            try:
                # 删除feedback表的外键约束
                cursor.execute("""
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'feedback') THEN
                        ALTER TABLE feedback DROP CONSTRAINT IF EXISTS feedback_message_id_fkey;
                    END IF;
                END
                $$;
                """)

                # 删除messages表的外键约束
                cursor.execute("""
                DO $$
                BEGIN
                    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'messages') THEN
                        ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_session_id_fkey;
                    END IF;
                END
                $$;
                """)

                conn.commit()
                logger.info("外键约束已删除")
            except Exception as e:
                logger.warning(f"删除外键约束时出错，可能不存在: {str(e)}")
                conn.rollback()

            # 创建会话表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建消息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS messages (
                message_id TEXT PRIMARY KEY,
                session_id TEXT NOT NULL,
                user_message TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                thinking_process TEXT,
                source_documents TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建反馈表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS feedback (
                feedback_id TEXT PRIMARY KEY,
                message_id TEXT NOT NULL,
                rating INTEGER NOT NULL,
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            conn.commit()

            # 创建永久存储反馈表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS permanent_feedback (
                id TEXT PRIMARY KEY,
                original_message_id TEXT,
                original_session_id TEXT,
                user_message TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                thinking_process TEXT,
                source_documents TEXT,
                rating INTEGER NOT NULL,
                comment TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE
            )
            ''')
            conn.commit()

            # 添加外键约束
            try:
                # 添加messages表的外键约束
                cursor.execute('''
                ALTER TABLE messages
                ADD CONSTRAINT messages_session_id_fkey
                FOREIGN KEY (session_id) REFERENCES sessions (session_id) ON DELETE CASCADE
                ''')

                # 添加feedback表的外键约束
                cursor.execute('''
                ALTER TABLE feedback
                ADD CONSTRAINT feedback_message_id_fkey
                FOREIGN KEY (message_id) REFERENCES messages (message_id) ON DELETE CASCADE
                ''')

                conn.commit()
                logger.info("外键约束添加成功")
            except Exception as e:
                logger.warning(f"添加外键约束时出错，可能已存在: {str(e)}")
                conn.rollback()

            conn.close()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    # 会话相关方法
    def save_session(self, session_id, title):
        """保存或更新会话"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查会话是否已存在
            cursor.execute("SELECT * FROM sessions WHERE session_id = %s", (session_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有会话
                cursor.execute(
                    "UPDATE sessions SET title = %s, updated_at = %s WHERE session_id = %s",
                    (title, datetime.now(), session_id)
                )
            else:
                # 创建新会话
                cursor.execute(
                    "INSERT INTO sessions (session_id, title, created_at, updated_at) VALUES (%s, %s, %s, %s)",
                    (session_id, title, datetime.now(), datetime.now())
                )

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存会话失败: {str(e)}")
            return False

    def get_sessions(self):
        """获取所有会话"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM sessions ORDER BY updated_at DESC")
            sessions = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for session in sessions:
                if 'created_at' in session and session['created_at']:
                    session['created_at'] = session['created_at'].isoformat()
                if 'updated_at' in session and session['updated_at']:
                    session['updated_at'] = session['updated_at'].isoformat()

            conn.close()
            return sessions
        except Exception as e:
            logger.error(f"获取会话失败: {str(e)}")
            return []

    def delete_session(self, session_id):
        """删除会话及其所有消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 标记永久存储表中的相关记录为已删除
            cursor.execute("""
                UPDATE permanent_feedback
                SET is_deleted = TRUE
                WHERE original_session_id = %s
            """, (session_id,))

            # 删除会话的所有反馈
            cursor.execute(
                "DELETE FROM feedback WHERE message_id IN (SELECT message_id FROM messages WHERE session_id = %s)",
                (session_id,)
            )

            # 删除会话的所有消息
            cursor.execute("DELETE FROM messages WHERE session_id = %s", (session_id,))

            # 删除会话
            cursor.execute("DELETE FROM sessions WHERE session_id = %s", (session_id,))

            conn.commit()
            conn.close()
            logger.info(f"会话 {session_id} 已删除，相关反馈已在永久存储表中标记为已删除")
            return True
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False

    # 消息相关方法
    def save_message(self, message_id, session_id, user_message, assistant_response, thinking_process=None, source_documents=None):
        """保存消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 确保会话存在
            cursor.execute("SELECT * FROM sessions WHERE session_id = %s", (session_id,))
            session = cursor.fetchone()

            if not session:
                # 如果会话不存在，创建一个新会话
                title = user_message[:30] + "..." if len(user_message) > 30 else user_message
                cursor.execute(
                    "INSERT INTO sessions (session_id, title, created_at, updated_at) VALUES (%s, %s, %s, %s)",
                    (session_id, title, datetime.now(), datetime.now())
                )

            # 保存消息
            cursor.execute(
                "INSERT INTO messages (message_id, session_id, user_message, assistant_response, thinking_process, source_documents) VALUES (%s, %s, %s, %s, %s, %s)",
                (message_id, session_id, user_message, assistant_response, thinking_process, source_documents)
            )

            # 更新会话的更新时间
            cursor.execute(
                "UPDATE sessions SET updated_at = %s WHERE session_id = %s",
                (datetime.now(), session_id)
            )

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存消息失败: {str(e)}")
            return False

    def get_session_messages(self, session_id):
        """获取会话的所有消息"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM messages WHERE session_id = %s ORDER BY created_at", (session_id,))
            messages = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for message in messages:
                if 'created_at' in message and message['created_at']:
                    message['created_at'] = message['created_at'].isoformat()

            conn.close()
            return messages
        except Exception as e:
            logger.error(f"获取会话消息失败: {str(e)}")
            return []

    # 反馈相关方法
    def save_feedback(self, feedback_id, message_id, rating, comment=None):
        """保存反馈"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # 检查消息是否存在
            cursor.execute("SELECT * FROM messages WHERE message_id = %s", (message_id,))
            message = cursor.fetchone()

            if not message:
                logger.error(f"消息不存在: {message_id}")
                conn.close()
                return False

            # 检查是否已有反馈
            cursor.execute("SELECT * FROM feedback WHERE message_id = %s", (message_id,))
            existing = cursor.fetchone()

            if existing:
                # 更新现有反馈
                cursor.execute(
                    "UPDATE feedback SET rating = %s, comment = %s WHERE message_id = %s",
                    (rating, comment, message_id)
                )
            else:
                # 创建新反馈
                cursor.execute(
                    "INSERT INTO feedback (feedback_id, message_id, rating, comment) VALUES (%s, %s, %s, %s)",
                    (feedback_id, message_id, rating, comment)
                )

            # 获取消息所属的会话信息
            cursor.execute("SELECT session_id FROM messages WHERE message_id = %s", (message_id,))
            session_result = cursor.fetchone()
            session_id = session_result['session_id'] if session_result else None

            if session_id:
                # 同时保存到永久存储表
                permanent_id = f"perm_{uuid.uuid4()}"

                # 将消息转换为字典格式以便于访问字段
                message_dict = dict(message)

                cursor.execute("""
                    INSERT INTO permanent_feedback (
                        id, original_message_id, original_session_id,
                        user_message, assistant_response, thinking_process, source_documents,
                        rating, comment, created_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    permanent_id, message_id, session_id,
                    message_dict['user_message'], message_dict['assistant_response'],
                    message_dict['thinking_process'], message_dict['source_documents'],
                    rating, comment, datetime.now()
                ))

                logger.info(f"消息 {message_id} 的反馈已永久保存，ID: {permanent_id}")

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"保存反馈失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False

    def get_message_feedback(self, message_id):
        """获取消息的反馈"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            cursor.execute("SELECT * FROM feedback WHERE message_id = %s", (message_id,))
            feedback = [dict(row) for row in cursor.fetchall()]

            # 将datetime对象转换为字符串
            for fb in feedback:
                if 'created_at' in fb and fb['created_at']:
                    fb['created_at'] = fb['created_at'].isoformat()

            conn.close()
            return feedback
        except Exception as e:
            logger.error(f"获取消息反馈失败: {str(e)}")
            return []

    def get_permanent_feedback(self, limit=100, offset=0, rating=None, include_deleted=False):
        """获取永久存储的反馈数据"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

            # 构建查询
            query = "SELECT * FROM permanent_feedback"
            query_params = []
            where_clauses = []

            # 根据参数添加过滤条件
            if rating is not None:
                where_clauses.append("rating = %s")
                query_params.append(int(rating))

            if not include_deleted:
                where_clauses.append("is_deleted = FALSE")

            # 添加WHERE子句
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # 添加排序和分页
            query += " ORDER BY created_at DESC LIMIT %s OFFSET %s"
            query_params.extend([limit, offset])

            # 执行查询
            cursor.execute(query, query_params)
            feedback_data = [dict(row) for row in cursor.fetchall()]

            # 处理datetime对象
            for item in feedback_data:
                if 'created_at' in item and item['created_at']:
                    item['created_at'] = item['created_at'].isoformat()

            conn.close()
            return feedback_data
        except Exception as e:
            logger.error(f"获取永久反馈数据失败: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()
            return []

    def get_permanent_feedback_count(self, rating=None, include_deleted=False):
        """获取永久存储的反馈数据总数"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 构建查询
            query = "SELECT COUNT(*) FROM permanent_feedback"
            query_params = []
            where_clauses = []

            # 根据参数添加过滤条件
            if rating is not None:
                where_clauses.append("rating = %s")
                query_params.append(int(rating))

            if not include_deleted:
                where_clauses.append("is_deleted = FALSE")

            # 添加WHERE子句
            if where_clauses:
                query += " WHERE " + " AND ".join(where_clauses)

            # 执行查询
            cursor.execute(query, query_params)
            count = cursor.fetchone()[0]

            conn.close()
            return count
        except Exception as e:
            logger.error(f"获取永久反馈数据总数失败: {str(e)}")
            if 'conn' in locals() and conn:
                conn.close()
            return 0

    def mark_permanent_feedback_deleted(self, feedback_id):
        """标记永久存储的反馈数据为已删除"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # 检查反馈是否存在
            cursor.execute("SELECT * FROM permanent_feedback WHERE id = %s", (feedback_id,))
            feedback = cursor.fetchone()

            if not feedback:
                logger.warning(f"反馈不存在: {feedback_id}")
                conn.close()
                return False

            # 标记为已删除
            cursor.execute(
                "UPDATE permanent_feedback SET is_deleted = TRUE WHERE id = %s",
                (feedback_id,)
            )

            conn.commit()
            conn.close()
            logger.info(f"反馈 {feedback_id} 已标记为已删除")
            return True
        except Exception as e:
            logger.error(f"标记反馈为已删除失败: {str(e)}")
            conn.rollback() if 'conn' in locals() else None
            if 'conn' in locals() and conn:
                conn.close()
            return False
