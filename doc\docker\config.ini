[PATHS]
# LibreOffice 可执行文件路径
LIBREOFFICE_PATH = /usr/bin/soffice
# Tesseract OCR 可执行文件路径
TESSERACT_PATH = /usr/bin

[DATABASE]
# 向量数据库存储目录，VECTOR_DB_TYPE可选值: chroma, qdrant
PERSIST_DIRECTORY = /app/data/db
VECTOR_DB_TYPE = qdrant

[DOCUMENTS]
# 源文档目录
SOURCE_DIRECTORY = /app/data/documents

[EMBEDDINGS]
# 嵌入模型路径
MODEL_NAME = /app/Private_GPT/sentence-transformers/bge-large-zh-v1.5
# 嵌入模型类型: huggingface, ollama
EMBEDDING_TYPE = ollama
# Ollama服务的基础URL，当EMBEDDING_TYPE=ollama时使用
BASE_URL = http://ollama:11434
# Ollama模型名称，当EMBEDDING_TYPE=ollama时使用
OLLAMA_MODEL = bge-large:latest

[CHUNKING]
# 文本分块大小
CHUNK_SIZE = 1000
# 文本分块重叠大小
CHUNK_OVERLAP = 100

[LLM]
# 大语言模型名称：deepseek-r1:32b、qwq
MODEL = deepseek-r1:32b
# 检索时返回的文档块数量
TARGET_SOURCE_CHUNKS = 5

[OCR]
# OCR 语言设置
LANGUAGES = chi_sim+eng

[QDRANT]
HOST = qdrant
PORT = 6333
# 存储集合名称，如果为空则使用文件夹名称
COLLECTION_NAME_STORE = documents
# 查询集合名称，如果为空则使用最新的集合
COLLECTION_NAME_QUERY = documents 