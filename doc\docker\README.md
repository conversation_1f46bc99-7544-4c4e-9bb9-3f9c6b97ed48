# RAG知识库问答系统 Docker 部署指南

本目录包含了使用Docker部署RAG知识库问答系统所需的所有文件。

## 文件说明

- `Dockerfile`: Web应用容器的构建文件
- `docker-compose.yml`: 多容器编排配置文件
- `config.ini`: Docker环境的配置文件
- `build.sh`: 构建脚本，用于准备Docker构建环境
- `nginx.conf`: Nginx反向代理配置示例
- `.dockerignore`: Docker构建时忽略的文件

## 快速开始

### 1. 准备环境

确保服务器已安装Docker和Docker Compose:

```bash
# 安装Docker (Ubuntu示例)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

如果需要GPU支持，请安装NVIDIA Container Toolkit:

```bash
# 添加NVIDIA软件包仓库
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 安装NVIDIA Container Toolkit
sudo apt-get update
sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker
```

### 2. 拉取模型

在部署前，需要确保Ollama服务器上有所需的模型:

```bash
# 拉取嵌入模型
ollama pull bge-large:latest

# 拉取大语言模型
ollama pull deepseek-r1:32b
```

### 3. 部署步骤

1. 将项目文件传输到服务器
2. 进入项目目录，执行构建和启动命令:

```bash
# 赋予构建脚本执行权限
chmod +x build.sh

# 执行构建脚本
./build.sh

# 进入构建目录
cd build

# 启动容器
docker-compose -f docker/docker-compose.yml up -d
```

3. 检查容器状态:

```bash
docker-compose -f docker/docker-compose.yml ps
```

4. 查看日志:

```bash
docker-compose -f docker/docker-compose.yml logs -f web
```

### 4. 访问应用

部署完成后，可以通过以下URL访问应用:

```
http://服务器IP:8089
```

## 使用Nginx作为反向代理

如果需要使用Nginx作为反向代理，可以修改`docker-compose.yml`文件，添加Nginx服务:

```yaml
nginx:
  image: nginx:latest
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - ./nginx.conf:/etc/nginx/conf.d/default.conf
    - ./ssl:/etc/nginx/ssl
  depends_on:
    - web
  restart: unless-stopped
```

然后使用提供的`nginx.conf`文件作为配置。

## 数据持久化

Docker Compose配置中已设置了数据卷，确保以下数据可以持久化:

- Qdrant数据库存储在`qdrant_data`卷中
- Ollama模型存储在`ollama_data`卷中
- 上传的文档和应用日志通过挂载主机目录实现持久化

## 故障排除

### 容器启动失败

检查日志:
```bash
docker-compose logs web
```

### 模型加载问题

确保Ollama容器有足够的资源加载模型:
```bash
docker stats ollama
```

### 文件权限问题

确保挂载的卷有正确的权限:
```bash
docker-compose exec web ls -la /app/data
```

## 备份数据

定期备份重要数据:

```bash
# 创建备份目录
mkdir -p backups

# 备份Qdrant数据
docker run --rm -v qdrant_data:/data -v $(pwd)/backups:/backups \
  ubuntu tar -czf /backups/qdrant_backup_$(date +%Y%m%d).tar.gz /data

# 备份上传的文档
docker run --rm -v $(pwd)/data:/data -v $(pwd)/backups:/backups \
  ubuntu tar -czf /backups/documents_backup_$(date +%Y%m%d).tar.gz /data/documents
```

更详细的部署说明，请参考`../docker_deployment.md`文件。 